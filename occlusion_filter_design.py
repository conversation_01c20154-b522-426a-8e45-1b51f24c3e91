#!/usr/bin/env python3
"""
Occlusion-Based Filter Design
VisionAware-Annotations Visual-Debug Sprint #3

PSEUDOCODE for selective rendering of occlusion-level 3 & 4 objects:

def should_render_object(obj_data, camera_name):
    '''
    Filter logic: Render object iff:
    - occlusion_level ∈ {3, 4} AND
    - per_camera_occlusion[camera_name] ≠ null
    '''
    
    # Extract visibility data
    visibility = obj_data.get('visibility', {})
    
    # Check occlusion level requirement
    occlusion_level = visibility.get('occlusion_level')
    if occlusion_level not in [3, 4]:
        return False, f"occlusion_level {occlusion_level} not in [3,4]"
    
    # Check per-camera occlusion data
    per_camera_occlusion = visibility.get('per_camera_occlusion', {})
    camera_occlusion = per_camera_occlusion.get(camera_name)
    
    if camera_occlusion is None:
        return False, f"per_camera_occlusion[{camera_name}] is null"
    
    return True, f"Level {occlusion_level}, occlusion {camera_occlusion:.3f}"

def create_occlusion_label(occlusion_level, camera_occlusion):
    '''
    Create label in format: O{level}:{value:.2f}
    Examples: "O3:0.87", "O4:0.91"
    '''
    return f"O{occlusion_level}:{camera_occlusion:.2f}"

def draw_red_white_label(img, label_text, position):
    '''
    Draw label with red background and white text
    at top-left corner of projected box
    '''
    x, y = position
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.6
    thickness = 2
    
    # Calculate text size
    (text_width, text_height), baseline = cv2.getTextSize(
        label_text, font, font_scale, thickness)
    
    # Red background rectangle
    bg_top_left = (x, y - text_height - baseline - 4)
    bg_bottom_right = (x + text_width + 8, y)
    cv2.rectangle(img, bg_top_left, bg_bottom_right, (0, 0, 255), -1)  # Red
    
    # White text
    text_pos = (x + 4, y - baseline - 2)
    cv2.putText(img, label_text, text_pos, font, font_scale, (255, 255, 255), thickness)

def draw_complete_box_projections(img, cam, corners):
    '''
    Modified version that doesn't truncate at image edges
    - Projects all corners
    - Draws lines even if endpoints are outside image bounds
    - Clips lines to image boundaries using cv2.clipLine
    '''
    H, W = img.shape[:2]
    uvz = cam.project(corners)
    pts = []
    
    # Project all points, keep even those outside image
    for u, v, z in uvz:
        if z <= 0:
            pts.append(None)  # Behind camera
        else:
            pts.append((int(round(u)), int(round(v))))
    
    # Draw edges with clipping
    edges = [(0,1),(1,2),(2,3),(3,0),(4,5),(5,6),(6,7),(7,4),(0,4),(1,5),(2,6),(3,7)]
    for i, j in edges:
        if pts[i] is not None and pts[j] is not None:
            # Use cv2.clipLine to handle edge cases
            retval, pt1, pt2 = cv2.clipLine((0, 0, W, H), pts[i], pts[j])
            if retval:
                cv2.line(img, pt1, pt2, (0, 255, 0), 2)  # Green boxes

# INTEGRATION POINTS:
# 1. Replace visibility-based filtering in _visualize_frame()
# 2. Remove FOV margin checking for occlusion objects
# 3. Use draw_complete_box_projections instead of draw_box_projections
# 4. Use draw_red_white_label for occlusion labels
"""
