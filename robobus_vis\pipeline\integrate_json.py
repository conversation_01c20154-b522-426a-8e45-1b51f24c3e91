import json
from pathlib import Path
from typing import Dict, Any

"""JSON integration: placeholder writer that will merge visibility fields into objects.
To be filled by pipeline once visibility results are computed.
"""


def write_augmented_json(src_json_path: str, dst_json_path: str, per_object_aug: Dict[int, Dict[str, Any]]):
    data = json.loads(Path(src_json_path).read_text(encoding='utf-8'))
    objects = data.get('result', {}).get('data', [])
    for obj in objects:
        oid = obj.get('ObjectID')
        if oid in per_object_aug:
            obj.update(per_object_aug[oid])
    Path(dst_json_path).parent.mkdir(parents=True, exist_ok=True)
    Path(dst_json_path).write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding='utf-8')
