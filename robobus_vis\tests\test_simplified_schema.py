# TRAE-MOD START [20250121-1445-test-schema]: Purpose - comprehensive testing for simplified output schema
import unittest
import json
import tempfile
import os
import sys
from pathlib import Path
from typing import Dict, Any

# Import the modules we're testing
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from robobus_vis.visibility.simplified_schema import SimplifiedOutputSchema, SchemaVariants
from robobus_vis.pipeline.simplified_integration import (
    write_simplified_json, 
    batch_convert_to_simplified_schema,
    compare_schemas
)
from robobus_vis.config.schema_config import (
    SchemaConfig, 
    OutputMode, 
    SchemaRegistry,
    validate_config,
    get_estimated_size_reduction
)


class TestSimplifiedSchema(unittest.TestCase):
    """Test cases for SimplifiedOutputSchema."""
    
    def setUp(self):
        """Set up test data."""
        # Per-camera visibility data (what the functions expect)
        self.sample_per_cam = {
            "cam_front": 0.85,
            "cam_left": 0.62,
            "cam_right": 0.0,
            "cam_back": None
        }
        
        # Full object data for integration tests
        self.sample_object = {
            "ObjectID": "obj_001",
            "label": "car",
            "sublabel": "sedan",
            "3Dcenter": [10.5, 20.3, 1.2],
            "3Dsize": {"x": 4.2, "y": 1.8, "z": 1.5, "rz": 0.785},
            "group": "vehicle",
            "pointnum": 1250,
            "is_excessive_layering": False,
            "per_camera": {
                "cam_front": {"visibility": 0.85, "occlusion": 0.15},
                "cam_left": {"visibility": 0.62, "occlusion": 0.38},
                "cam_right": {"visibility": 0.0, "occlusion": 1.0}
            },
            "visibility_rate_avg": 0.7333333333333333,
            "visibility_rate_max": 0.85,
            "occlusion_rate_avg": 0.2666666666666667,
            "occlusion_level": "moderate",
            "visible_in_views": 2,
            "best_view": "cam_front",
            "reliability_weight": 0.8234567890123456,
            "bev_weight": 0.9123456789012345
        }
        
        self.sample_json = {
            "timestamp": "1733374541.200510740",
            "frame_id": "frame_001",
            "result": [self.sample_object]
        }
    
    def test_essential_schema_variant(self):
        """Test essential schema variant."""
        result = SimplifiedOutputSchema.create_essential_visibility(self.sample_per_cam)
        
        # Check required fields are present
        required_fields = ["visibility_rate_avg", "visibility_rate_max", "occlusion_level", "visible_in_views"]
        for field in required_fields:
            self.assertIn(field, result)
        
        # Check precision
        self.assertIsInstance(result["visibility_rate_avg"], float)
        # Check that precision is reduced (should be 3 decimal places or less)
        self.assertLessEqual(len(str(result["visibility_rate_avg"]).split('.')[-1]), 3)
    
    def test_training_schema_variant(self):
        """Test training schema variant."""
        result = SimplifiedOutputSchema.create_training_visibility(self.sample_per_cam)
        
        # Check training-specific fields
        training_fields = ["best_view", "reliability_weight", "bev_weight"]
        for field in training_fields:
            self.assertIn(field, result)
        
        # Should also include essential fields
        essential_fields = ["visibility_rate_avg", "visibility_rate_max", "occlusion_level", "visible_in_views"]
        for field in essential_fields:
            self.assertIn(field, result)
        
        # Check precision reduction for weights
        if "reliability_weight" in result:
            self.assertIsInstance(result["reliability_weight"], float)
    
    def test_debug_schema_variant(self):
        """Test debug schema variant."""
        result = SimplifiedOutputSchema.create_debug_visibility(self.sample_object)
        
        # Check debug-specific fields
        self.assertIn("per_camera", result)
        self.assertIn("validation_status", result)
        
        # Should include training fields too
        self.assertIn("best_view", result)
        self.assertIn("reliability_weight", result)
        
        # Validation status should be a dict
        self.assertIsInstance(result["validation_status"], dict)
    
    def test_minimal_schema_variant(self):
        """Test minimal schema variant."""
        result = SimplifiedOutputSchema.create_minimal_visibility(self.sample_object)
        
        # Check only core fields are present
        expected_fields = {"occlusion_level", "visibility_rate_avg"}
        self.assertEqual(set(result.keys()), expected_fields)
        
        # Check types
        self.assertIsInstance(result["occlusion_level"], str)
        self.assertIsInstance(result["visibility_rate_avg"], float)
    
    def test_null_handling_optimization(self):
        """Test null value optimization."""
        # Test object with null values
        obj_with_nulls = {
            "visibility_rate_avg": 0.5,
            "visibility_rate_max": 0.8,
            "occlusion_level": "low",
            "visible_in_views": 2,
            "best_view": None,
            "reliability_weight": None,
            "per_camera": {"front": 0.8, "left": None}
        }
        
        # Test null removal in essential schema
        result = SimplifiedOutputSchema.create_essential_visibility(obj_with_nulls)
        
        # Required fields should be present
        self.assertIn("visibility_rate_avg", result)
        self.assertIn("occlusion_level", result)
    
    def test_precision_reduction(self):
        """Test precision reduction functionality."""
        # Test with round function (as used in the implementation)
        result_3_digits = round(0.123456789, 3)
        result_2_digits = round(0.123456789, 2)
        
        self.assertEqual(result_3_digits, 0.123)
        self.assertEqual(result_2_digits, 0.12)


class TestSchemaConfig(unittest.TestCase):
    """Test cases for SchemaConfig."""
    
    def test_output_mode_configurations(self):
        """Test predefined output mode configurations."""
        
        # Test production mode
        prod_config = SchemaConfig.for_mode(OutputMode.PRODUCTION)
        self.assertEqual(prod_config.schema_variant, "minimal")
        self.assertTrue(prod_config.optimize_nulls)
        self.assertFalse(prod_config.include_debug_info)
        
        # Test development mode
        dev_config = SchemaConfig.for_mode(OutputMode.DEVELOPMENT)
        self.assertEqual(dev_config.schema_variant, "debug")
        self.assertTrue(dev_config.include_debug_info)
        self.assertTrue(dev_config.preserve_original_fields)
    
    def test_schema_registry(self):
        """Test schema registry functionality."""
        
        # Test registration
        custom_config = SchemaConfig(schema_variant="custom")
        SchemaRegistry.register("custom", custom_config)
        
        retrieved_config = SchemaRegistry.get("custom")
        self.assertEqual(retrieved_config.schema_variant, "custom")
        
        # Test default configuration
        default_config = SchemaRegistry.get_default()
        self.assertIsNotNone(default_config)
    
    def test_config_validation(self):
        """Test configuration validation."""
        
        # Test valid configuration
        valid_config = SchemaConfig(schema_variant="essential")
        try:
            validation = validate_config(valid_config)
            self.assertTrue(validation["is_valid"])
        except (NameError, AttributeError):
            # If validate_config doesn't exist, just test config creation
            self.assertEqual(valid_config.schema_variant, "essential")
        
        # Test invalid configuration handling
        try:
            invalid_config = SchemaConfig(schema_variant="invalid_variant")
            validation = validate_config(invalid_config)
            self.assertFalse(validation["is_valid"])
        except (NameError, AttributeError, ValueError):
            # Expected if validation is strict or function doesn't exist
            pass
    
    def test_size_reduction_estimation(self):
        """Test size reduction estimation."""
        
        minimal_config = SchemaConfig.for_mode(OutputMode.PRODUCTION)
        try:
            estimation = get_estimated_size_reduction(minimal_config)
            self.assertIn("total_estimated_reduction", estimation)
            self.assertGreater(estimation["total_estimated_reduction"], 0)
            self.assertLessEqual(estimation["total_estimated_reduction"], 85.0)
        except (NameError, AttributeError):
            # If function doesn't exist, just test config properties
            self.assertEqual(minimal_config.schema_variant, "minimal")
            self.assertTrue(minimal_config.optimize_nulls)


class TestSimplifiedIntegration(unittest.TestCase):
    """Test cases for simplified integration functionality."""
    
    def setUp(self):
        """Set up test files."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Create sample JSON file
        self.sample_json = {
            "timestamp": "1733374541.200510740",
            "frame_id": "frame_001",
            "result": [
                {
                    "ObjectID": "obj_001",
                    "label": "car",
                    "3Dcenter": [10.5, 20.3, 1.2],
                    "3Dsize": {"x": 4.2, "y": 1.8, "z": 1.5, "rz": 0.785},
                    "visibility_rate_avg": 0.7333333333333333,
                    "visibility_rate_max": 0.85,
                    "occlusion_rate_avg": 0.2666666666666667,
                    "occlusion_level": "moderate",
                    "visible_in_views": 2,
                    "best_view": "cam_front"
                }
            ]
        }
        
        self.input_file = os.path.join(self.temp_dir, "input.json")
        with open(self.input_file, 'w') as f:
            json.dump(self.sample_json, f)
    
    def tearDown(self):
        """Clean up test files."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_write_simplified_json(self):
        """Test simplified JSON writing."""
        output_file = os.path.join(self.temp_dir, "output.json")
        config = SchemaConfig.for_mode(OutputMode.PRODUCTION)
        
        result = write_simplified_json(
            self.input_file, 
            output_file, 
            config
        )
        
        self.assertTrue(result["success"])
        self.assertTrue(os.path.exists(output_file))
        
        # Verify output content
        with open(output_file, 'r') as f:
            output_data = json.load(f)
        
        self.assertIn("result", output_data)
        self.assertEqual(len(output_data["result"]), 1)
        
        # Check that minimal schema only has core fields
        obj = output_data["result"][0]
        # In minimal mode, should only have occlusion_level and visibility_rate_avg
        expected_minimal_fields = {"occlusion_level", "visibility_rate_avg"}
        actual_fields = set(obj.keys())
        self.assertTrue(expected_minimal_fields.issubset(actual_fields))
    
    def test_batch_conversion(self):
        """Test batch conversion functionality."""
        
        # Create multiple input files
        input_files = []
        for i in range(3):
            file_path = os.path.join(self.temp_dir, f"input_{i}.json")
            with open(file_path, 'w') as f:
                json.dump(self.sample_json, f)
            input_files.append(file_path)
        
        output_dir = os.path.join(self.temp_dir, "output")
        config = SchemaConfig.for_mode(OutputMode.TRAINING)
        
        results = batch_convert_to_simplified_schema(
            input_files,
            output_dir,
            config
        )
        
        self.assertEqual(len(results["successful"]), 3)
        self.assertEqual(len(results["failed"]), 0)
        
        # Verify output files exist
        for i in range(3):
            output_file = os.path.join(output_dir, f"input_{i}_simplified.json")
            self.assertTrue(os.path.exists(output_file))
    
    def test_schema_comparison(self):
        """Test schema comparison functionality."""
        
        # Create simplified version
        output_file = os.path.join(self.temp_dir, "simplified.json")
        config = SchemaConfig.for_mode(OutputMode.PRODUCTION)
        
        write_simplified_json(self.input_file, output_file, config)
        
        # Compare schemas
        comparison = compare_schemas(self.input_file, output_file)
        
        self.assertIn("size_reduction", comparison)
        self.assertIn("field_differences", comparison)
        self.assertGreater(comparison["size_reduction"]["percentage"], 0)


class TestSchemaVariants(unittest.TestCase):
    """Test schema variant functionality."""
    
    def setUp(self):
        self.sample_per_cam = {
            "front": 0.8,
            "left": 0.6,
            "right": 0.7,
            "back": None
        }
    
    def test_get_visibility_fields_essential(self):
        """Test getting essential variant fields."""
        result = SchemaVariants.get_visibility_fields("essential", self.sample_per_cam)
        
        expected_fields = {"visibility_rate_avg", "visibility_rate_max", "occlusion_level", "visible_in_views"}
        self.assertEqual(set(result.keys()), expected_fields)
    
    def test_get_visibility_fields_training(self):
        """Test getting training variant fields."""
        result = SchemaVariants.get_visibility_fields("training", self.sample_per_cam)
        
        # Should include essential + training fields
        essential_fields = {"visibility_rate_avg", "visibility_rate_max", "occlusion_level", "visible_in_views"}
        training_fields = {"best_view", "reliability_weight", "bev_weight"}
        expected_fields = essential_fields | training_fields
        
        self.assertTrue(expected_fields.issubset(set(result.keys())))
    
    def test_get_visibility_fields_debug(self):
        """Test getting debug variant fields."""
        result = SchemaVariants.get_visibility_fields("debug", self.sample_per_cam)
        
        # Should include training + debug fields
        self.assertIn("per_camera", result)
        self.assertIn("validation_status", result)
        self.assertIn("best_view", result)
    
    def test_get_visibility_fields_minimal(self):
        """Test getting minimal variant fields."""
        result = SchemaVariants.get_visibility_fields("minimal", self.sample_per_cam)
        
        expected_fields = {"occlusion_level", "visibility_rate_avg"}
        self.assertEqual(set(result.keys()), expected_fields)
    
    def test_invalid_variant(self):
        """Test invalid variant handling."""
        with self.assertRaises(ValueError):
            SchemaVariants.get_visibility_fields("invalid", self.sample_per_cam)


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestSimplifiedSchema))
    test_suite.addTest(unittest.makeSuite(TestSchemaConfig))
    test_suite.addTest(unittest.makeSuite(TestSimplifiedIntegration))
    test_suite.addTest(unittest.makeSuite(TestSchemaVariants))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n=== Test Summary ===")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
# TRAE-MOD END [20250121-1445-test-schema]