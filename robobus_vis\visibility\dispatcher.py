from typing import Any, Dict, <PERSON><PERSON>, Optional
import numpy as np
import logging

from .sphere_projection import visibility_via_sphere_projection

logger = logging.getLogger(__name__)


def compute_visibility(
    box3d: Dict,
    cam,
    depth_img: np.ndarray,
    cfg: Dict,
    method_arg: str,
    samples: int,
    enable_diagnostics: bool,
    debug_dir: Optional[str],
    obj_id: Any,
    cam_name: str,
) -> <PERSON><PERSON>[float, Dict]:
    """
    Compute visibility using Sphere Projection (single, production method).

    Notes:
    - The dispatcher is simplified to a single method (sphere) and ignores
      method_arg and samples for compatibility with existing callers.
    - Configuration keys read from cfg['visibility']:
        sphere_radius_m, tau_base_m, tau_scale_per_m,
        occlusion_fraction_thr, min_neighbors,
        treat_no_depth_as_visible, max_window_px
    """
    vis_cfg = cfg.get('visibility', {})

    # Sphere-only path
    r, st = visibility_via_sphere_projection(
        box3d,
        cam,
        depth_img,
        sphere_radius_m=vis_cfg.get('sphere_radius_m', 0.15),
        tau_base_m=vis_cfg.get('tau_base_m', 0.3),
        tau_scale_per_m=vis_cfg.get('tau_scale_per_m', 0.02),
        occlusion_fraction_thr=vis_cfg.get('occlusion_fraction_thr', 0.2),
        min_neighbors=vis_cfg.get('min_neighbors', 3),
        treat_no_depth_as_visible=vis_cfg.get('treat_no_depth_as_visible', True),
        max_window_px=vis_cfg.get('max_window_px', 15),
    )

    # Diagnostics are handled inside sphere_projection when enabled via cfg.
    return r, st

