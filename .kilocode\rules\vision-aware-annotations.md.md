## Brief overview
This rule file establishes comprehensive guidelines for the VisionAware Annotations system, which implements advanced 3D object visibility calculation using the Sphere Projection Visibility Algorithm as the core computational method. The system processes multi-camera vehicle data to generate accurate visibility annotations for autonomous driving perception training.

## Current Project Status (Phase 5 - Production Ready)
- **Core Algorithm**: Sphere Projection Visibility Algorithm fully integrated and tested
- **System Status**: Production-ready with comprehensive functionality
- **Performance**: ~2-5ms per object per camera, 40-60% improvement in sparse point cloud handling
- **Integration**: Complete end-to-end pipeline with multi-method support (sphere, zbuffer, rays)

## Communication style
- Use precise technical terminology when discussing computer vision, 3D geometry, and sensor fusion concepts
- Reference specific algorithm implementations: Sphere Projection, Z-buffer, Ray Sampling
- Include mathematical formulations for visibility calculations and coordinate transformations
- Document configuration parameters with YAML examples and practical ranges
- Emphasize performance metrics and accuracy improvements in technical discussions

## Development workflow
- **Algorithm-Centric Development**: All new features must integrate with Sphere Projection as primary method
- **Configuration-Driven Design**: Use YAML configs for all algorithm parameters and thresholds
- **Validation First**: Test against known geometric configurations before real-world data
- **Documentation Synchronization**: Update docs/sphere_projection_algorithm.md with any algorithm changes
- **Performance Benchmarking**: Measure against baseline Z-buffer method for each optimization

## Sphere Projection Algorithm Implementation
### Core Mathematical Foundation
- **Pixel Radius Calculation**: `r = max(fx, fy) * sphere_radius_m / z`
- **Adaptive Tolerance**: `τ = max(tau_base_m, tau_scale_per_m * z)`
- **Occlusion Analysis**: Neighborhood-based depth distribution analysis
- **Visibility Classification**: Fraction-based occlusion thresholding

### Key Configuration Parameters
```yaml
visibility:
  method_primary: sphere
  sphere_radius_m: 0.15
  occlusion_fraction_thr: 0.2
  tau_base_m: 0.3
  tau_scale_per_m: 0.02
  max_window_px: 15
```

### Integration Requirements
- **Method Dispatcher**: Clean routing between sphere, zbuffer, and rays methods
- **Fallback Strategy**: Automatic fallback to ray sampling for sparse data
- **Output Enhancement**: Include per_camera_occlusion and occlusion_rate_avg fields
- **CLI Support**: Add 'sphere' method option with 'auto' as default

## Coding best practices
- **Algorithm Modularity**: Implement sphere projection as standalone module with clear interfaces
- **Parameter Validation**: Validate all sphere projection parameters at initialization
- **Error Handling**: Robust handling for edge cases (zero radius, out-of-bounds projections)
- **Performance Optimization**: Use cv2.circle() for accurate circular mask generation
- **Memory Efficiency**: Streaming processing for large datasets with O(1) space complexity

## Project structure and key components
```
robobus_vis/
├── visibility/
│   ├── sphere_projection.py      # Core sphere algorithm implementation
│   ├── per_camera_pixel_ratio.py # Method dispatcher and integration
│   └── surface_ray_sampling.py   # Fallback method
├── pipeline/
│   └── run_batch.py             # End-to-end processing with sphere integration
├── configs/
│   └── default.yaml             # Sphere projection configuration
└── docs/
    └── sphere_projection_algorithm.md  # Detailed algorithm documentation
```

## Data handling guidelines
- **Input Validation**: Verify calibrated_sensor.pb.txt and pose.txt before processing
- **Temporal Synchronization**: Apply SE(3) motion compensation using ego_pose_interpolation_SE3
- **FOV Filtering**: Use ego-sector gating and camera FOV validation before sphere projection
- **Quality Assurance**: Check visibility_confidence > 0.5 for reliable sphere projection results
- **Output Compatibility**: Maintain backward compatibility with existing JSON formats

## Testing strategies
- **Geometric Validation**: Test sphere projection with synthetic cube configurations
- **Sparse Data Testing**: Validate 40-60% improvement over Z-buffer for sparse point clouds
- **Parameter Sensitivity**: Test sphere_radius_m from 0.1m to 0.5m across different scenarios
- **Integration Testing**: Run full pipeline with sphere method on clip_dataset_1
- **Performance Benchmarking**: Compare sphere vs zbuffer processing times per object

## Documentation requirements
### Mandatory Documentation Updates
- **Algorithm Changes**: Update docs/sphere_projection_algorithm.md for any parameter or logic changes
- **Configuration Updates**: Document new sphere projection parameters in YAML examples
- **Performance Reports**: Include sphere projection metrics in all technical reports
- **Integration Notes**: Document method dispatcher behavior and fallback strategies
