from dataclasses import dataclass
from typing import Optional
import numpy as np
import cv2

@dataclass
class Camera:
    name: str
    K: np.ndarray  # 3x3
    dist: Optional[np.ndarray]
    width: int
    height: int
    T_base_cam: np.ndarray  # 4x4 (cam<-base)

    def __post_init__(self):
        # Precompute rvec/tvec for cv2.projectPoints: X_cam = R*X_base + t
        R = self.T_base_cam[:3, :3]
        t = self.T_base_cam[:3, 3]
        self._rvec, _ = cv2.Rodrigues(R.astype(np.float64))
        self._tvec = t.astype(np.float64).reshape(3,1)
        self._K = self.K.astype(np.float64)
        self._dist = None if self.dist is None else self.dist.astype(np.float64).reshape(-1,1)

    def project(self, pts_base: np.ndarray):
        """Project base-frame points into image using cv2.projectPoints if dist provided; fallback to pinhole.
        T_base_cam must be cam<-base.
        Returns Nx3: (u,v,z) with z in camera depth.
        """
        if pts_base.size == 0:
            return np.zeros((0,3), dtype=float)
        # Compute depth in camera frame regardless
        pts_h = np.concatenate([pts_base[:,:3], np.ones((pts_base.shape[0],1))], axis=1)
        P = (self.T_base_cam @ pts_h.T).T[:, :3]
        z = P[:,2].astype(np.float64)
        if self._dist is not None:
            img_pts, _ = cv2.projectPoints(P.astype(np.float64), np.zeros((3,1)), np.zeros((3,1)), self._K, self._dist)
            uv = img_pts.reshape(-1,2)
            u = uv[:,0]
            v = uv[:,1]
        else:
            x = np.divide(P[:,0], z, out=np.zeros_like(z), where=z!=0)
            y = np.divide(P[:,1], z, out=np.zeros_like(z), where=z!=0)
            u = self.K[0,0]*x + self.K[0,2]
            v = self.K[1,1]*y + self.K[1,2]
        return np.stack([u, v, z], axis=1)
