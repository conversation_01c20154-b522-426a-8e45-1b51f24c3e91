{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:\\Work_project\\D6-ULDV\\project_code\\visionaware_annotations"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "mcp-taskmanager": {"command": "npx", "args": ["-y", "@kazuph/mcp-taskmanager"]}, "code-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@block/code-mcp", "--key", "99b68a9d-3b9c-4d18-8f01-69aabdcf4a71"], "disabled": false, "alwaysAllow": []}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}, "mcp-fetch": {"command": "npx", "args": ["-y", "@kazuph/mcp-fetch"]}}}