import numpy as np
from ..calib.transforms import inv_T, transform_points, se3_interpolate


def delta_T(pose_stream, t_from: float, t_to: float) -> np.ndarray:
    T_w_b_from = pose_stream.at(t_from)
    T_w_b_to   = pose_stream.at(t_to)
    return T_w_b_to @ inv_T(T_w_b_from)


def compensate_points_to_time(pose_stream, pts_base: np.ndarray, t_from: float, t_to: float) -> np.ndarray:
    dT = delta_T(pose_stream, t_from, t_to)
    return transform_points(dT, pts_base)
