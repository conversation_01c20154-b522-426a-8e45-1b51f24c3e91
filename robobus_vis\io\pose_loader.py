from typing import Dict, List, <PERSON><PERSON>
import numpy as np
from ..calib.transforms import se3_interpolate

class PoseStream:
    """SE(3) pose stream from pose.txt (t + 16 numbers, 4x4 row-major per line).
    Provides interpolation via SO(3) geodesic and linear translation.
    """
    def __init__(self, txt_path: str):
        self.txt_path = txt_path
        self._poses: List[Tuple[float, np.ndarray]] = []  # list of (t, T_4x4)
        self._load()

    def _load(self):
        try:
            with open(self.txt_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    parts = line.split()
                    if len(parts) != 17:
                        continue
                    t = float(parts[0])
                    mat = np.array(list(map(float, parts[1:])), dtype=float).reshape(4,4)
                    self._poses.append((t, mat))
            self._poses.sort(key=lambda x: x[0])
        except FileNotFoundError:
            self._poses = []

    def at(self, t: float) -> np.ndarray:
        if not self._poses:
            return np.eye(4, dtype=float)
        # boundary
        if t <= self._poses[0][0]:
            return self._poses[0][1]
        if t >= self._poses[-1][0]:
            return self._poses[-1][1]
        # binary search
        lo, hi = 0, len(self._poses) - 1
        while lo <= hi:
            mid = (lo + hi) // 2
            tm = self._poses[mid][0]
            if tm < t:
                lo = mid + 1
            else:
                hi = mid - 1
        i1 = lo
        i0 = lo - 1
        t0, T0 = self._poses[i0]
        t1, T1 = self._poses[i1]
        s = (t - t0) / (t1 - t0 + 1e-12)
        return se3_interpolate(T0, T1, s)

    def health(self) -> Dict:
        ok = True
        ortho_errs = []
        for _, T in self._poses:
            R = T[:3,:3]
            ortho = R.T @ R - np.eye(3)
            e = np.linalg.norm(ortho)
            ortho_errs.append(e)
            if e > 1e-2:
                ok = False
        return {"loaded": len(self._poses), "ok": ok, "max_R_ortho_error": float(max(ortho_errs) if ortho_errs else 0.0)}
