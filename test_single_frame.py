#!/usr/bin/env python3
"""
Single frame test for VisionAware-Annotations Visual-Debug Sprint #3
Test occlusion-level 3 & 4 filtering and red/white labels
"""

import logging
from pathlib import Path
from batch_visualization import BatchVisualizationProcessor

# Enable debug logging to see filtering decisions
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_single_frame():
    """Test the new occlusion-based filtering on a single frame."""
    
    # Create processor with test output directory
    processor = BatchVisualizationProcessor(
        demo_data_dir="visibility_demo_data/clip_dataset_1",
        result_dir="result_test/clip_dataset_1",
        output_dir="result_test/clip_dataset_1/vis_result_sprint3_test",
        calib_file="visibility_demo_data/clip_dataset_1/calibrated_sensor.pb.txt",
        config_path="configs/default.yaml"
    )
    
    # Test on the problematic frame that has occlusion level 3 & 4 objects
    test_json = Path("result_test/clip_dataset_1/1733374541.500515937.json")
    
    print("=== TESTING SINGLE FRAME ===")
    print(f"Processing: {test_json}")
    
    success = processor._visualize_frame(test_json)
    
    if success:
        print("✅ Single frame processing successful!")
        
        # Check output directory structure
        output_dir = Path("result_test/clip_dataset_1/vis_result_sprint3_test")
        frame_dir = output_dir / "1733374541.500515937"
        
        if frame_dir.exists():
            print(f"✅ Frame directory created: {frame_dir}")
            
            # List camera subdirectories
            camera_dirs = [d for d in frame_dir.iterdir() if d.is_dir()]
            print(f"✅ Found {len(camera_dirs)} camera directories:")
            for cam_dir in camera_dirs:
                images = list(cam_dir.glob("*.jpg"))
                print(f"  - {cam_dir.name}: {len(images)} images")
                
        else:
            print("❌ Frame directory not created")
            
    else:
        print("❌ Single frame processing failed!")
    
    return success

if __name__ == "__main__":
    test_single_frame()
