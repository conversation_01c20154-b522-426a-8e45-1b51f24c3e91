from pathlib import Path
from typing import Dict, Any, List

IMAGE_DIR_EXCLUDES = {"3d_url", "result_json", "result_pcd"}

class ClipDataset:
    """Loader for a single clip directory following the RoboBUS structure.
    Provides path discovery and frame indexing by timestamp-based filenames.
    """
    def __init__(self, clip_dir: str):
        self.root = Path(clip_dir)
        assert self.root.exists(), f"Clip dir not found: {clip_dir}"
        # Camera image subdirs (e.g., 60_front, 120_front, ...)
        self.image_dirs = {
            d.name: d for d in self.root.iterdir() if d.is_dir() and d.name not in IMAGE_DIR_EXCLUDES
        }
        self.pcd_dir = self.root / "3d_url"
        self.json_dir = self.root / "result_json"
        self.pose_txt = self.root / "pose.txt"
        self.calib_pb_txt = self.root / "calibrated_sensor.pb.txt"

    def list_timestamps(self) -> List[str]:
        """List sorted timestamps present in both point cloud (.pcd) and json annotations.
        Filenames are like 1733374539.800821065.pcd / .json .
        """
        pcd_ts = {p.stem for p in (self.pcd_dir.glob('*.pcd') if self.pcd_dir.exists() else [])}
        json_ts = {p.stem for p in (self.json_dir.glob('*.json') if self.json_dir.exists() else [])}
        ts = sorted(pcd_ts & json_ts)
        return ts

    def frame_paths_by_ts(self, ts: str) -> Dict[str, Any]:
        """Return paths for a timestamp across sensors.
        Images assumed to be named <ts>.jpg under each camera dir.
        PCD path is 3d_url/<ts>.pcd; JSON is result_json/<ts>.json
        """
        data = {
            "images": {name: p / f"{ts}.jpg" for name, p in self.image_dirs.items()},
            "pcd": self.pcd_dir / f"{ts}.pcd",
            "json": self.json_dir / f"{ts}.json",
            "pose": self.pose_txt,
            "calib": self.calib_pb_txt,
        }
        return data
