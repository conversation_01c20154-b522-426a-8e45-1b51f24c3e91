# TRAE-MOD START [20250121-1435-simplified-integration]: Purpose - integrate simplified schema into JSON output pipeline
import json
from pathlib import Path
from typing import Dict, Any, Optional
from ..visibility.simplified_schema import (
    create_object_visibility_data,
    SchemaVariants,
    optimize_null_handling
)


def write_simplified_json(src_json_path: str, 
                         dst_json_path: str, 
                         per_object_aug: Dict[int, Dict[str, Any]],
                         schema_variant: str = SchemaVariants.ESSENTIAL,
                         optimize_nulls: bool = True,
                         preserve_original_fields: bool = False) -> Dict[str, Any]:
    """
    Write JSON with simplified visibility schema.
    
    Args:
        src_json_path: Source JSON file path
        dst_json_path: Destination JSON file path
        per_object_aug: Per-object augmentation data
        schema_variant: Schema variant to use (essential, training, debug, minimal)
        optimize_nulls: Whether to optimize null value handling
        preserve_original_fields: Whether to preserve original non-visibility fields
        
    Returns:
        Dictionary with processing statistics
    """
    # Load source JSON
    data = json.loads(Path(src_json_path).read_text(encoding='utf-8'))
    
    # Get objects from result data
    if 'result' in data and isinstance(data['result'], list):
        objects = data['result']
    elif 'result' in data and 'data' in data['result']:
        objects = data['result']['data']
    elif 'result' in data:
        objects = data['result']
    else:
        objects = data.get('data', [])
    
    # Processing statistics
    stats = {
        "total_objects": len(objects),
        "processed_objects": 0,
        "schema_variant": schema_variant,
        "optimize_nulls": optimize_nulls,
        "size_reduction": 0.0
    }
    
    original_size = len(json.dumps(data))
    
    # Process each object
    for obj in objects:
        oid = obj.get('ObjectID')
        if oid in per_object_aug:
            aug_data = per_object_aug[oid]
            
            # Extract per-camera visibility data
            per_cam = {}
            stats_total = None
            
            if 'visibility' in aug_data:
                visibility_section = aug_data['visibility']
                
                # Extract per-camera data
                if 'per_camera' in visibility_section:
                    per_cam = visibility_section['per_camera']
                
                # Extract statistics if available
                if 'stats_total' in aug_data:
                    stats_total = aug_data['stats_total']
            
            # Create simplified visibility data
            if per_cam:
                simplified_visibility = create_object_visibility_data(
                    per_cam=per_cam,
                    stats_total=stats_total,
                    schema_variant=schema_variant,
                    optimize_nulls=optimize_nulls
                )
                
                # Update object with simplified visibility
                if preserve_original_fields:
                    # Preserve original fields and add simplified visibility
                    obj.update(aug_data)
                    obj['visibility_simplified'] = simplified_visibility
                else:
                    # Replace with simplified schema
                    # Preserve non-visibility fields
                    non_visibility_fields = {k: v for k, v in aug_data.items() 
                                           if k not in ['visibility', 'per_camera', 'stats_total']}
                    obj.update(non_visibility_fields)
                    obj['visibility'] = simplified_visibility
                
                stats["processed_objects"] += 1
    
    # Calculate size reduction
    new_size = len(json.dumps(data))
    if original_size > 0:
        stats["size_reduction"] = (original_size - new_size) / original_size * 100
    
    # Write output JSON
    Path(dst_json_path).parent.mkdir(parents=True, exist_ok=True)
    Path(dst_json_path).write_text(
        json.dumps(data, ensure_ascii=False, indent=2), 
        encoding='utf-8'
    )
    
    return stats


def batch_convert_to_simplified_schema(input_dir: str, 
                                      output_dir: str,
                                      schema_variant: str = SchemaVariants.ESSENTIAL,
                                      file_pattern: str = "*.json") -> Dict[str, Any]:
    """
    Batch convert JSON files to simplified schema.
    
    Args:
        input_dir: Input directory containing JSON files
        output_dir: Output directory for simplified JSON files
        schema_variant: Schema variant to use
        file_pattern: File pattern to match (default: "*.json")
        
    Returns:
        Dictionary with batch processing statistics
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Find JSON files
    json_files = list(input_path.glob(file_pattern))
    
    batch_stats = {
        "total_files": len(json_files),
        "processed_files": 0,
        "failed_files": [],
        "total_objects": 0,
        "total_processed_objects": 0,
        "average_size_reduction": 0.0,
        "schema_variant": schema_variant
    }
    
    size_reductions = []
    
    for json_file in json_files:
        try:
            # Load and analyze existing JSON to extract visibility data
            data = json.loads(json_file.read_text(encoding='utf-8'))
            
            # Extract per-object augmentation data from existing JSON
            per_object_aug = {}
            
            # Get objects from result data
            if 'result' in data and isinstance(data['result'], list):
                objects = data['result']
            elif 'result' in data and 'data' in data['result']:
                objects = data['result']['data']
            elif 'result' in data:
                objects = data['result']
            else:
                objects = data.get('data', [])
            
            # Extract existing visibility data for conversion
            for obj in objects:
                oid = obj.get('ObjectID')
                if oid is not None:
                    # Extract visibility-related fields
                    aug_data = {}
                    
                    if 'visibility' in obj:
                        aug_data['visibility'] = obj['visibility']
                    
                    # Extract individual visibility fields if present
                    visibility_fields = [
                        'per_camera', 'visibility_rate_avg', 'visibility_rate_max',
                        'occlusion_rate_avg', 'occlusion_level', 'visible_in_views',
                        'best_view', 'pointnum', 'is_excessive_layering'
                    ]
                    
                    for field in visibility_fields:
                        if field in obj:
                            if 'visibility' not in aug_data:
                                aug_data['visibility'] = {}
                            aug_data['visibility'][field] = obj[field]
                    
                    if aug_data:
                        per_object_aug[oid] = aug_data
            
            # Convert to simplified schema
            output_file = output_path / json_file.name
            
            # Create a copy of original data for conversion
            original_data = json.loads(json_file.read_text(encoding='utf-8'))
            
            stats = write_simplified_json(
                src_json_path=str(json_file),
                dst_json_path=str(output_file),
                per_object_aug=per_object_aug,
                schema_variant=schema_variant,
                optimize_nulls=True,
                preserve_original_fields=False
            )
            
            batch_stats["processed_files"] += 1
            batch_stats["total_objects"] += stats["total_objects"]
            batch_stats["total_processed_objects"] += stats["processed_objects"]
            
            if stats["size_reduction"] > 0:
                size_reductions.append(stats["size_reduction"])
            
        except Exception as e:
            batch_stats["failed_files"].append({
                "file": str(json_file),
                "error": str(e)
            })
    
    # Calculate average size reduction
    if size_reductions:
        batch_stats["average_size_reduction"] = sum(size_reductions) / len(size_reductions)
    
    return batch_stats


def compare_schemas(original_json_path: str, simplified_json_path: str) -> Dict[str, Any]:
    """
    Compare original and simplified JSON schemas.
    
    Args:
        original_json_path: Path to original JSON file
        simplified_json_path: Path to simplified JSON file
        
    Returns:
        Dictionary with comparison results
    """
    # Load both files
    original_data = json.loads(Path(original_json_path).read_text(encoding='utf-8'))
    simplified_data = json.loads(Path(simplified_json_path).read_text(encoding='utf-8'))
    
    # Calculate sizes
    original_size = len(json.dumps(original_data))
    simplified_size = len(json.dumps(simplified_data))
    
    # Analyze field differences
    def extract_fields(data, prefix=""):
        fields = set()
        if isinstance(data, dict):
            for key, value in data.items():
                field_name = f"{prefix}.{key}" if prefix else key
                fields.add(field_name)
                if isinstance(value, (dict, list)):
                    fields.update(extract_fields(value, field_name))
        elif isinstance(data, list) and data:
            fields.update(extract_fields(data[0], prefix))
        return fields
    
    original_fields = extract_fields(original_data)
    simplified_fields = extract_fields(simplified_data)
    
    comparison = {
        "size_reduction": {
            "original_size": original_size,
            "simplified_size": simplified_size,
            "reduction_bytes": original_size - simplified_size,
            "reduction_percentage": (original_size - simplified_size) / original_size * 100 if original_size > 0 else 0
        },
        "field_analysis": {
            "original_field_count": len(original_fields),
            "simplified_field_count": len(simplified_fields),
            "removed_fields": list(original_fields - simplified_fields),
            "added_fields": list(simplified_fields - original_fields),
            "preserved_fields": list(original_fields & simplified_fields)
        }
    }
    
    return comparison
# TRAE-MOD END [20250121-1435-simplified-integration]