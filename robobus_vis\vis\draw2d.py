import numpy as np
import cv2

COLORS = {
    'box': (0, 255, 0),
    'text_bg': (0, 0, 0),
    'text_fg': (255, 255, 255),
    'proj': (0, 200, 255),
    'visible': (0, 255, 0),
    'occluded': (0, 0, 255),
    'occlusion_label_bg': (255, 0, 0),  # Blue background for occlusion labels
    'occlusion_label_fg': (255, 255, 255),  # White text for occlusion labels
}


def put_text(img, text, org=(10,20)):
    cv2.rectangle(img, (org[0]-3, org[1]-15), (org[0]+300, org[1]+5), COLORS['text_bg'], -1)
    cv2.putText(img, text, org, cv2.FONT_HERSHEY_SIMPLEX, 0.5, COLORS['text_fg'], 1, cv2.LINE_AA)


def draw_projected_points(img, cam, pts_base, radius=1):
    uvz = cam.project(pts_base)
    H, W = img.shape[:2]
    for u, v, z in uvz:
        if z <= 0:
            continue
        ui, vi = int(round(u)), int(round(v))
        if 0 <= ui < W and 0 <= vi < H:
            cv2.circle(img, (ui,vi), radius, COLORS['proj'], -1)


def draw_box_projections(img, cam, corners):
    H, W = img.shape[:2]
    uvz = cam.project(corners)
    pts = []
    for u, v, z in uvz:
        if z <= 0:
            pts.append(None)
            continue
        ui, vi = int(round(u)), int(round(v))
        if 0 <= ui < W and 0 <= vi < H:
            pts.append((ui,vi))
        else:
            pts.append(None)
    # draw edges if both endpoints valid
    edges = [(0,1),(1,2),(2,3),(3,0),(4,5),(5,6),(6,7),(7,4),(0,4),(1,5),(2,6),(3,7)]
    for i,j in edges:
        if pts[i] is not None and pts[j] is not None:
            cv2.line(img, pts[i], pts[j], COLORS['box'], 2)


def overlay_visibility_score(img, score: float, org=(10,40)):
    put_text(img, f"vis={score:.2f}", org)


# TRAE-MOD START [20250121-1430-occlusion]: Purpose - add occlusion label drawing function
def draw_occlusion_label(img, occlusion_level, occlusion_rate_avg, box_top_left):
    """
    Draw occlusion information label at the top-left corner of a detection box.
    
    Args:
        img: Input image (numpy array)
        occlusion_level: Occlusion level (integer)
        occlusion_rate_avg: Average occlusion rate (float)
        box_top_left: Top-left corner coordinates of the detection box (tuple: x, y)
    """
    x, y = box_top_left
    
    # Create label text
    label_text = f"OL:{occlusion_level} OR:{occlusion_rate_avg:.2f}"
    
    # Calculate text size
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.5
    thickness = 1
    (text_width, text_height), baseline = cv2.getTextSize(label_text, font, font_scale, thickness)
    
    # Define label background rectangle
    label_bg_top_left = (x, y - text_height - baseline - 4)
    label_bg_bottom_right = (x + text_width + 8, y)
    
    # Ensure label stays within image bounds
    H, W = img.shape[:2]
    label_bg_top_left = (max(0, label_bg_top_left[0]), max(0, label_bg_top_left[1]))
    label_bg_bottom_right = (min(W, label_bg_bottom_right[0]), min(H, label_bg_bottom_right[1]))
    
    # Draw blue background rectangle
    cv2.rectangle(img, label_bg_top_left, label_bg_bottom_right, COLORS['occlusion_label_bg'], -1)
    
    # Draw white text
    text_org = (label_bg_top_left[0] + 4, label_bg_bottom_right[1] - baseline - 2)
    cv2.putText(img, label_text, text_org, font, font_scale, COLORS['occlusion_label_fg'], thickness, cv2.LINE_AA)
# TRAE-MOD END [20250121-1430-occlusion]


def make_mosaic(images, cols=3, pad=4, bg=(30,30,30)):
    import math
    if not images:
        return None
    Hs = [im.shape[0] for im in images]
    Ws = [im.shape[1] for im in images]
    H = max(Hs)
    W = max(Ws)
    rows = math.ceil(len(images)/cols)
    canvas = np.full((rows*H + (rows+1)*pad, cols*W + (cols+1)*pad, 3), bg, dtype=np.uint8)
    for idx, im in enumerate(images):
        r = idx // cols
        c = idx % cols
        y0 = pad + r*(H+pad)
        x0 = pad + c*(W+pad)
        h, w = im.shape[:2]
        canvas[y0:y0+h, x0:x0+w] = im
    return canvas
