# TRAE-MOD START [20250121-1440-schema-config]: Purpose - configuration management for simplified output schemas
from typing import Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass


class OutputMode(Enum):
    """Output mode enumeration for different use cases."""
    PRODUCTION = "production"      # Minimal schema for production deployment
    TRAINING = "training"          # Optimized for model training
    DEVELOPMENT = "development"    # Full debug information
    ANALYSIS = "analysis"          # Comprehensive metrics for analysis


@dataclass
class SchemaConfig:
    """Configuration for output schema generation."""
    
    # Schema variant selection
    schema_variant: str = "essential"  # essential, training, debug, minimal
    
    # Optimization settings
    optimize_nulls: bool = True
    reduce_precision: bool = True
    precision_digits: int = 3
    
    # Field inclusion settings
    include_per_camera: bool = False
    include_derived_fields: bool = True
    include_debug_info: bool = False
    include_statistics: bool = False
    
    # Compatibility settings
    preserve_original_fields: bool = False
    backward_compatible: bool = False
    
    # Performance settings
    enable_compression: bool = False
    batch_processing: bool = True
    
    @classmethod
    def for_mode(cls, mode: OutputMode) -> 'SchemaConfig':
        """Create schema configuration for specific output mode."""
        
        if mode == OutputMode.PRODUCTION:
            return cls(
                schema_variant="minimal",
                optimize_nulls=True,
                reduce_precision=True,
                precision_digits=2,
                include_per_camera=False,
                include_derived_fields=False,
                include_debug_info=False,
                include_statistics=False,
                preserve_original_fields=False,
                backward_compatible=False,
                enable_compression=True,
                batch_processing=True
            )
        
        elif mode == OutputMode.TRAINING:
            return cls(
                schema_variant="training",
                optimize_nulls=True,
                reduce_precision=True,
                precision_digits=3,
                include_per_camera=False,
                include_derived_fields=True,
                include_debug_info=False,
                include_statistics=True,
                preserve_original_fields=False,
                backward_compatible=False,
                enable_compression=False,
                batch_processing=True
            )
        
        elif mode == OutputMode.DEVELOPMENT:
            return cls(
                schema_variant="debug",
                optimize_nulls=False,
                reduce_precision=False,
                precision_digits=6,
                include_per_camera=True,
                include_derived_fields=True,
                include_debug_info=True,
                include_statistics=True,
                preserve_original_fields=True,
                backward_compatible=True,
                enable_compression=False,
                batch_processing=False
            )
        
        elif mode == OutputMode.ANALYSIS:
            return cls(
                schema_variant="debug",
                optimize_nulls=False,
                reduce_precision=True,
                precision_digits=4,
                include_per_camera=True,
                include_derived_fields=True,
                include_debug_info=True,
                include_statistics=True,
                preserve_original_fields=False,
                backward_compatible=False,
                enable_compression=False,
                batch_processing=True
            )
        
        else:
            # Default to essential
            return cls()


class SchemaRegistry:
    """Registry for managing different schema configurations."""
    
    _configs: Dict[str, SchemaConfig] = {}
    _default_config: Optional[SchemaConfig] = None
    
    @classmethod
    def register(cls, name: str, config: SchemaConfig) -> None:
        """Register a schema configuration."""
        cls._configs[name] = config
    
    @classmethod
    def get(cls, name: str) -> Optional[SchemaConfig]:
        """Get a registered schema configuration."""
        return cls._configs.get(name)
    
    @classmethod
    def set_default(cls, config: SchemaConfig) -> None:
        """Set the default schema configuration."""
        cls._default_config = config
    
    @classmethod
    def get_default(cls) -> SchemaConfig:
        """Get the default schema configuration."""
        if cls._default_config is None:
            cls._default_config = SchemaConfig.for_mode(OutputMode.PRODUCTION)
        return cls._default_config
    
    @classmethod
    def list_configs(cls) -> Dict[str, SchemaConfig]:
        """List all registered configurations."""
        return cls._configs.copy()
    
    @classmethod
    def initialize_defaults(cls) -> None:
        """Initialize default configurations."""
        # Register predefined configurations
        cls.register("production", SchemaConfig.for_mode(OutputMode.PRODUCTION))
        cls.register("training", SchemaConfig.for_mode(OutputMode.TRAINING))
        cls.register("development", SchemaConfig.for_mode(OutputMode.DEVELOPMENT))
        cls.register("analysis", SchemaConfig.for_mode(OutputMode.ANALYSIS))
        
        # Set production as default
        cls.set_default(SchemaConfig.for_mode(OutputMode.PRODUCTION))


def get_field_mapping() -> Dict[str, Dict[str, str]]:
    """Get field mapping between original and simplified schemas."""
    return {
        "visibility": {
            "per_camera": "per_camera",  # Preserved in debug mode
            "visibility_rate_avg": "visibility_rate_avg",  # Core field
            "visibility_rate_max": "visibility_rate_max",  # Core field
            "occlusion_rate_avg": None,  # REMOVED - redundant (can derive from visibility_rate_avg)
            "occlusion_level": "occlusion_level",  # Core field
            "visible_in_views": "visible_in_views",  # Core field
            "best_view": "best_view"  # Training mode field
        },
        "derived_fields": {
            "reliability_weight": "reliability_weight",  # Training mode
            "bev_weight": "bev_weight",  # Training mode
            "validation_status": "validation_status"  # Debug mode
        },
        "metadata": {
            "pointnum": "pointnum",  # Preserved
            "is_excessive_layering": "is_excessive_layering"  # Preserved
        }
    }


def validate_config(config: SchemaConfig) -> Dict[str, Any]:
    """Validate schema configuration and return validation results."""
    validation = {
        "is_valid": True,
        "warnings": [],
        "errors": []
    }
    
    # Check schema variant
    valid_variants = ["essential", "training", "debug", "minimal"]
    if config.schema_variant not in valid_variants:
        validation["errors"].append(
            f"Invalid schema_variant '{config.schema_variant}'. Must be one of: {valid_variants}"
        )
        validation["is_valid"] = False
    
    # Check precision settings
    if config.reduce_precision and config.precision_digits < 1:
        validation["errors"].append(
            "precision_digits must be >= 1 when reduce_precision is True"
        )
        validation["is_valid"] = False
    
    if config.precision_digits > 10:
        validation["warnings"].append(
            "precision_digits > 10 may not provide significant benefit and increases file size"
        )
    
    # Check compatibility settings
    if config.preserve_original_fields and config.optimize_nulls:
        validation["warnings"].append(
            "preserve_original_fields=True with optimize_nulls=True may not reduce file size significantly"
        )
    
    # Check mode consistency
    if config.schema_variant == "minimal" and config.include_debug_info:
        validation["warnings"].append(
            "include_debug_info=True with schema_variant='minimal' has no effect"
        )
    
    if config.schema_variant == "debug" and not config.include_debug_info:
        validation["warnings"].append(
            "schema_variant='debug' typically should have include_debug_info=True"
        )
    
    return validation


def get_estimated_size_reduction(config: SchemaConfig) -> Dict[str, float]:
    """Estimate size reduction based on configuration settings."""
    
    # Base reduction estimates (percentages)
    base_reductions = {
        "minimal": 60.0,
        "essential": 45.0,
        "training": 25.0,
        "debug": 10.0
    }
    
    base_reduction = base_reductions.get(config.schema_variant, 30.0)
    
    # Additional reductions
    additional_reduction = 0.0
    
    if config.optimize_nulls:
        additional_reduction += 5.0
    
    if config.reduce_precision:
        precision_reduction = max(0, (15 - config.precision_digits) * 2.0)
        additional_reduction += precision_reduction
    
    if not config.include_per_camera:
        additional_reduction += 15.0
    
    if not config.include_derived_fields:
        additional_reduction += 8.0
    
    if not config.include_debug_info:
        additional_reduction += 12.0
    
    total_reduction = min(base_reduction + additional_reduction, 85.0)  # Cap at 85%
    
    return {
        "base_reduction": base_reduction,
        "additional_reduction": additional_reduction,
        "total_estimated_reduction": total_reduction
    }


# Initialize default configurations
SchemaRegistry.initialize_defaults()
# TRAE-MOD END [20250121-1440-schema-config]