#!/usr/bin/env python3
import json

# Test visibility data for the problematic frame
with open('result_test/clip_dataset_1/1733374541.500515937.json', 'r') as f:
    data = json.load(f)

print('=== VISIBILITY DATA ANALYSIS ===')
for i, obj in enumerate(data['result']['data']):
    if 'visibility' in obj:
        vis = obj['visibility']
        obj_id = obj.get('ObjectID', 'N/A')
        print(f'Object {i} (ID: {obj_id}):')
        per_cam = vis.get('per_camera', {})
        for cam, val in per_cam.items():
            print(f'  {cam}: {val}')
        print()

print('=== EXPECTED BEHAVIOR ===')
print('Objects with null visibility should NOT be drawn in those cameras')
print('Objects with visibility < 0.15 should be filtered out')
print('Only cameras with meaningful visibility should show the object')
