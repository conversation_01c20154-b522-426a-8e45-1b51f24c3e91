#!/usr/bin/env python3
import json

# Analyze occlusion levels in the test data
with open('result_test/clip_dataset_1/1733374541.500515937.json', 'r') as f:
    data = json.load(f)

print('=== OCCLUSION LEVEL ANALYSIS ===')
level_counts = {}
level_3_4_objects = []

for i, obj in enumerate(data['result']['data']):
    if 'visibility' in obj:
        level = obj['visibility'].get('occlusion_level', 'N/A')
        level_counts[level] = level_counts.get(level, 0) + 1
        
        if level in [3, 4]:
            obj_id = obj.get('ObjectID', 'N/A')
            print(f'Object {i} (ID: {obj_id}): Level {level}')
            per_cam_occ = obj['visibility'].get('per_camera_occlusion', {})
            valid_cameras = []
            for cam, val in per_cam_occ.items():
                if val is not None:
                    print(f'  {cam}: {val:.3f}')
                    valid_cameras.append(cam)
            level_3_4_objects.append({
                'index': i,
                'id': obj_id,
                'level': level,
                'cameras': valid_cameras
            })
            print()

print('Level distribution:', level_counts)
print(f'Found {len(level_3_4_objects)} objects with occlusion level 3 or 4')

# Check current filtering issues
print('\n=== CURRENT FILTERING ANALYSIS ===')
print('Issues identified:')
print('1. FOV filtering with 50% margin may be too restrictive for edge objects')
print('2. Visibility threshold of 0.15 filters based on visibility, not occlusion level')
print('3. draw_box_projections truncates boxes at image edges (lines 41-44 in draw2d.py)')
print('4. Current system renders all visible objects, not just occlusion level 3&4')
