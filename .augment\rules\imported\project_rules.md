---
type: "manual"
---

<!-- TRAE-MOD START [********-1535-project-rules]: Purpose - populate governance rules for Trae AI operations in this workspace -->
# User Rules for Trae AI (Chat & Agent Behavior)

Purpose
- Establish project-wide governance for Trae AI to reliably solve complex projects with clear analysis, precise execution, and auditable outcomes.

Scope
- Applies to all Trae AI Chat and Agent activities within this workspace, including planning, code edits, tool usage, knowledge management, and approvals.

## 1. Core Principles
### 1.1 Thoughtful Analysis
- Perform detailed analysis of user prompts before responding.
- Break complex problems into logical steps with explicit assumptions and constraints.
- Maintain strict context awareness to prevent hallucinations; verify against repository state before claims.

### 1.2 Structured Cognition First
- Build and maintain a Project Knowledge Graph (PKG): modules, core classes/functions, key data structures, and end-to-end data flow.
- Update the PKG after any add/modify/delete operation to keep understanding current.

### 1.3 Hallucination Prevention
- Verify-first: cross-check files/paths before referring to them.
- Prefer minimal, testable changes; avoid extrapolating beyond visible context.

## 2. Precision in Code Modifications
- Surgical scope: modify only the necessary lines; avoid collateral edits.
- Explicit markers in code to denote modified regions.
- Full audit trail for every change.
- Preserve original functionality; use feature flags/guards when risk exists.

### 2.1 Region Markers (Mandatory)
Use language-appropriate comment styles to bracket changes, with a stable ID.

ID format: YYYYMMDD-HHMM-<short-slug>

Examples:
```python
# TRAE-MOD START [********-1030-setup]: Purpose - fix race condition in loader
# ... modified code ...
# TRAE-MOD END [********-1030-setup]
```
```js
// TRAE-MOD START [********-1102-api]: Purpose - add retry logic
// ... modified code ...
// TRAE-MOD END [********-1102-api]
```
```yaml
# TRAE-MOD START [********-1115-ci]: Purpose - cache pip deps
# ... modified config ...
# TRAE-MOD END [********-1115-ci]
```

### 2.2 Change Log Entry (Mandatory)
All edits must be recorded in agent_work_log.md with the template below:
```md
- ID: [YYYYMMDD-HHMM-slug]
- Title: [Short change title]
- Purpose: [Why]
- Affected Components: [Files/modules/classes/functions]
- Scope: [Lines/regions changed]
- Potential Side Effects: [Risks & mitigations]
- Tests/Validation: [What was run and results]
- Rollback Plan: [How to revert]
- Approval: [Requester/User approval status]
```

## 3. MCP-driven ReAct Execution
- Dynamic tool selection grounded in reasoning; document rationale and expected outcomes.
- Maintain a tool usage log when performing non-trivial operations:
```md
- Tool: [Name]
- Intent: [Why used]
- Inputs (high-level): [Summarized]
- Expected Outcome: [Before run]
- Actual Outcome: [After run]
```
- Never expose secrets; do not log credentials or tokens.

## 4. Operational Guidelines
### 4.1 Chat Behavior
- Retain conversation history context and explicitly reference decision points.
- Provide clear, concise explanations and cite uncertainties/assumptions.
- Flag risks (performance, security, data loss, API/ABI breaks) with severity.

### 4.2 Agent Behavior
- Problem Assessment:
  - Complexity Level (1-5):
    1. Trivial single-line or doc-only
    2. Small single-file change, low risk
    3. Multi-file moderate change, limited blast radius
    4. Cross-module significant change, notable risk
    5. Architectural/refactor or deployment-critical
  - Required Expertise Domains: e.g., Python, PyTorch, BEV/Transformer, optimization, deployment.
  - Scope/Impact Estimation: list affected areas, interfaces, performance/cost impact.

- Solution Execution Logging:
  - For each change, record Purpose, Affected Components, Side Effects, Tests/Validation, Rollback, Metrics.
  - Maintain an auditable trail in agent_work_log.md.

## 5. Knowledge Management
- Capture conversation keypoints and decisions for future reference.
- Store solution patterns and reusable snippets.
- Cross-link related issues and prior solutions.
- After resolution, update knowledge base materials under ./project_analysis/ and maintain long-term memory in memory_bank/.

## 6. Compliance Requirements
- Fact-check all outputs against project specifications and repository state.
- Code modifications require explicit user approval before proceeding to subsequent tasks.
- Ensure version control compatibility, deterministic builds, and reproducibility.
- Preserve original functionality; when uncertain, default to non-breaking changes.
- Security-first: avoid leaking secrets, sanitize logs, and respect privacy.

## 7. Performance Metrics
- Solution accuracy rate (issues resolved correctly).
- Context retention across sessions (consistency of references and decisions).
- Tool-choice quality (appropriate selection and minimal overhead).
- Code modification precision (delta size vs. effect; absence of regressions).
- Lead time per change and change failure rate (CFR).

## 8. Approval Workflow
- Present a brief plan before risky/complex changes (levels 3-5).
- After implementing a task, request explicit approval; do not proceed without it.
- Summarize what changed, how verified, and any follow-ups required.

## 9. Templates
### 9.1 Decision Log
```md
- Decision: [What]
- Context: [Background/alternatives]
- Rationale: [Why chosen]
- Risk: [Severity + mitigations]
- Owner: [Name/role]
- Date: [YYYY-MM-DD]
- Links: [PRs, commits, docs]
```

### 9.2 Knowledge Card
```md
- Topic: [Area]
- Problem: [What problem this solves]
- Pattern: [Solution pattern]
- Applicability: [When to use]
- Example: [Short snippet]
- References: [Links/paths]
```

## 10. Version Control Standards
- Branch naming: feature/<slug>, fix/<slug>, chore/<slug>.
- Commit message format:
```text
TRAE: <type>(<scope>): <summary>

<body: what/why>
<footer: refs/issues/IDs>
```
- Always reference TRAE-MOD IDs and Decision Log entries where applicable.

## 11. Risk Flags (Checklist)
- [ ] Data loss risk
- [ ] Performance regression risk
- [ ] API/ABI breaking change
- [ ] Security/privacy concern
- [ ] Build/deploy pipeline impact
- [ ] Third-party dependency risk

## 12. Standard Operating Procedure (SOP)
Analyze → Plan → Execute (surgical) → Verify (tests/metrics) → Log (audit) → Request Approval → Merge/Release → Update Knowledge Base.

## 13. File Index Requirements
- agent_work_log.md (root): mandatory change log and decisions.
- project_analysis/ (dir): analyses, designs, and postmortems.
- memory_bank/ (dir): long-term memory artifacts and embeddings.

---
By following this document, Trae AI will operate with thoughtful analysis, precise and auditable code changes, disciplined tool usage, and strong governance around approvals, knowledge, and metrics.
<!-- TRAE-MOD END [********-1535-project-rules] -->