#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch Visualization Script for Occlusion Results

This script processes JSON result files and visualizes occlusion information
on corresponding camera images using the sphere projection algorithm results.
Optimized for accurate per-camera projections with FOV gating and color coding.

Author: Trae AI Agent
Date: 2025-08-25
"""

import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
import yaml
from tqdm import tqdm

# Import project modules
from robobus_vis.vis.exporters import save_image
from robobus_vis.io.calib_parser import parse_calibrated_sensor_pb_txt
from robobus_vis.calib.camera_model import Camera
from robobus_vis.visibility.fov_filter import ego_sector_gate, cam_fov_gate_point

# TRAE-MOD START [20250825-1600-vis-opt]: Purpose - optimize batch visualization with FOV gating and color coding
# TRAE-MOD START [20250825-1630-rear-opt]: Purpose - enhance rear camera projection accuracy with per-camera visibility filtering
# TRAE-MOD START [20250825-1700-full-opt]: Purpose - fix projection errors across all cameras with comprehensive diagnostics

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BatchVisualizationProcessor:
    """
    Processes batch visualization of occlusion results on camera images.
    Optimized with FOV gating and color-coded occlusion visualization.
    """

    def __init__(self,
                 demo_data_dir: str,
                 result_dir: str,
                 output_dir: str,
                 calib_file: str,
                 config_path: str = None):
        """
        Initialize the batch visualization processor.

        Args:
            demo_data_dir: Directory containing camera images (visibility_demo_data/clip_dataset_1)
            result_dir: Directory containing JSON result files (result_test/clip_dataset_1)
            output_dir: Directory to save visualization results
            calib_file: Path to calibrated_sensor.pb.txt file
            config_path: Path to configuration file
        """
        self.demo_data_dir = Path(demo_data_dir)
        self.result_dir = Path(result_dir)
        self.output_dir = Path(output_dir)
        self.calib_file = calib_file

        # Load configuration
        if config_path is None:
            config_path = Path(__file__).resolve().parents[0] / 'configs' / 'default.yaml'
        self.config = yaml.safe_load(open(config_path, 'r'))

        # Enable diagnostics for comprehensive logging
        if 'visibility' not in self.config:
            self.config['visibility'] = {}
        self.config['visibility']['enable_diagnostics'] = True

        # Load FOV configuration and convert to radians
        self.camera_fovs_rad = {}
        self.ego_sectors_rad = {}

        # Convert camera FOVs from degrees to radians
        camera_fovs_deg = self.config.get('camera_fovs_deg', {})
        for cam_name, fov_deg in camera_fovs_deg.items():
            self.camera_fovs_rad[cam_name] = {
                'h': fov_deg['h'] * np.pi / 180.0,
                'v': fov_deg['v'] * np.pi / 180.0
            }

        # Convert ego sectors from degrees to radians
        ego_sectors_deg = self.config.get('ego_sector_map_deg', {})
        for cam_name, sector_deg in ego_sectors_deg.items():
            self.ego_sectors_rad[cam_name] = {
                'center': sector_deg['center'] * np.pi / 180.0,
                'half': sector_deg['half'] * np.pi / 180.0
            }

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Load camera calibrations
        self.cameras = self._load_cameras()

        logger.info(f"Initialized BatchVisualizationProcessor with FOV gating")
        logger.info(f"Demo data dir: {self.demo_data_dir}")
        logger.info(f"Result dir: {self.result_dir}")
        logger.info(f"Output dir: {self.output_dir}")
        logger.info(f"Found {len(self.cameras)} cameras")
        logger.info(f"Loaded FOV configs for {len(self.camera_fovs_rad)} cameras")
    
    def _load_cameras(self) -> Dict[str, Camera]:
        """
        Load camera calibrations using the camera mapping from config.
        Maps directory names to calibration sensor names.
        
        Returns:
            Dictionary mapping directory names to Camera objects
        """
        try:
            calibrations = parse_calibrated_sensor_pb_txt(self.calib_file)
            cameras = {}
            
            # Get camera mapping from config
            cam_map = self.config.get('camera_map', {})
            
            for dir_name, sensor_name in cam_map.items():
                if sensor_name not in calibrations.cams:
                    logger.warning(f"Sensor {sensor_name} not found in calibrations")
                    continue
                    
                c = calibrations.cams[sensor_name]
                cameras[dir_name] = Camera(
                    name=dir_name,  # Use directory name, not sensor name
                    K=c['K'],
                    dist=c['dist'],
                    width=c['width'],
                    height=c['height'],
                    T_base_cam=c['T_base_cam'],
                )
                
            logger.info(f"Loaded {len(cameras)} cameras: {list(cameras.keys())}")
            return cameras
            
        except Exception as e:
            logger.error(f"Failed to load camera calibrations: {e}")
            return {}
    
    def _find_camera_image(self, timestamp: str, camera_name: str) -> Optional[str]:
        """
        Find the camera image file for a given timestamp and camera.
        
        Args:
            timestamp: Timestamp string from JSON filename
            camera_name: Name of the camera
            
        Returns:
            Path to the image file if found, None otherwise
        """
        # Direct mapping for known camera names
        camera_dir_mapping = {
            '60_front': '60_front',
            '120_front': '120_front', 
            '120_back': '120_back',
            '120_left': '120_left',
            '120_right': '120_right',
            'left_back': 'left_back',
            'right_back': 'right_back'
        }
        
        # Try direct mapping first
        if camera_name in camera_dir_mapping:
            cam_dir_name = camera_dir_mapping[camera_name]
            cam_dir_path = self.demo_data_dir / cam_dir_name
            
            if cam_dir_path.exists() and cam_dir_path.is_dir():
                # Look for image file with matching timestamp
                image_files = list(cam_dir_path.glob(f"{timestamp}*.jpg"))
                if image_files:
                    return str(image_files[0])
                    
                # Also try without extension matching
                image_files = list(cam_dir_path.glob("*.jpg"))
                for img_file in image_files:
                    if timestamp in img_file.stem:
                        return str(img_file)
        
        # Fallback to fuzzy matching for other cameras
        camera_dirs = [d for d in self.demo_data_dir.iterdir() if d.is_dir()]
        
        for cam_dir in camera_dirs:
            if camera_name.lower() in cam_dir.name.lower() or cam_dir.name.lower() in camera_name.lower():
                # Look for image file with matching timestamp
                image_files = list(cam_dir.glob(f"{timestamp}*.jpg"))
                if image_files:
                    return str(image_files[0])
                    
                # Also try without extension matching
                image_files = list(cam_dir.glob("*.jpg"))
                for img_file in image_files:
                    if timestamp in img_file.stem:
                        return str(img_file)
        
        return None
    
    def _create_3d_box_from_json(self, obj_data: Dict) -> Dict:
        """
        Create a 3D box dictionary from JSON object data with proper rotation handling.

        Args:
            obj_data: Object data from JSON file

        Returns:
            Dictionary with 3D box information
        """
        # Extract center and size
        center = np.array([obj_data['3Dcenter']['x'], obj_data['3Dcenter']['y'], obj_data['3Dcenter']['z']])
        size_data = obj_data['3Dsize']

        # Extract dimensions
        length = size_data['length']
        width = size_data['width']
        height = size_data['height']

        # Extract rotation (use rz for yaw rotation around Z-axis)
        yaw = size_data.get('rz', size_data.get('alpha', 0.0))

        # Create local corner points (before rotation)
        dx, dy, dz = length/2, width/2, height/2
        local_corners = np.array([
            [-dx, -dy, -dz],  # 0: back-left-bottom
            [+dx, -dy, -dz],  # 1: back-right-bottom
            [+dx, +dy, -dz],  # 2: front-right-bottom
            [-dx, +dy, -dz],  # 3: front-left-bottom
            [-dx, -dy, +dz],  # 4: back-left-top
            [+dx, -dy, +dz],  # 5: back-right-top
            [+dx, +dy, +dz],  # 6: front-right-top
            [-dx, +dy, +dz],  # 7: front-left-top
        ])

        # Apply rotation around Z-axis (yaw)
        cos_yaw = np.cos(yaw)
        sin_yaw = np.sin(yaw)
        rotation_matrix = np.array([
            [cos_yaw, -sin_yaw, 0],
            [sin_yaw,  cos_yaw, 0],
            [0,        0,       1]
        ])

        # Rotate corners and translate to world position
        rotated_corners = local_corners @ rotation_matrix.T
        world_corners = rotated_corners + center

        return {
            'center': center,
            'size': np.array([length, width, height]),
            'surface_pts': world_corners,
            'corners': world_corners
        }

    def _get_box_color(self, occlusion_level: int) -> Tuple[int, int, int]:
        """
        Get color for 3D box based on occlusion level.

        Args:
            occlusion_level: Occlusion level (1-4)

        Returns:
            BGR color tuple for OpenCV
        """
        if occlusion_level in [1, 2]:
            return (0, 255, 0)  # Green for low occlusion
        elif occlusion_level == 3:
            return (0, 255, 255)  # Yellow for medium occlusion
        elif occlusion_level == 4:
            return (0, 0, 255)  # Red for high occlusion
        else:
            return (128, 128, 128)  # Gray for unknown/level 0

    def _is_object_in_camera_fov(self, box3d: Dict, camera_name: str, camera: Camera) -> Tuple[bool, str]:
        """
        Check if a 3D object is within the camera's field of view using proper FOV gating.
        Fixed depth calculation and enhanced diagnostics.

        Args:
            box3d: 3D bounding box dictionary with 'center' key
            camera_name: Name of the camera
            camera: Camera object with T_base_cam transformation

        Returns:
            Tuple of (is_in_fov: bool, reason: str)
        """
        center = box3d['center']

        # Step 1: Ego sector gating - check if object is in camera's angular sector
        center_ego = center[:2]  # x, y coordinates in ego frame
        in_sector, sector_reason = ego_sector_gate(center_ego, camera_name, self.ego_sectors_rad)
        if not in_sector:
            return False, f"ego_sector: {sector_reason}"

        # Step 2: Camera FOV gating - check if object is within camera's field of view
        center_3d = np.array([center[0], center[1], center[2]])
        in_fov, fov_reason = cam_fov_gate_point(
            center_3d,
            camera.T_base_cam,
            camera_name,
            self.camera_fovs_rad,
            z_forward_positive=True
        )
        if not in_fov:
            return False, f"cam_fov: {fov_reason}"

        # Step 3: Basic depth check - ensure object can be projected
        # Transform center to camera coordinate system
        T_cam_base = np.linalg.inv(camera.T_base_cam)
        center_cam = T_cam_base @ np.append(center, 1)

        # More lenient depth check - allow objects slightly behind camera for edge cases
        if center_cam[2] <= -1.0:  # Allow 1m tolerance behind camera
            return False, f"too_far_behind: z={center_cam[2]:.2f}"

        return True, f"in_fov: sector_ok, fov_ok, z={center_cam[2]:.2f}"

    def _draw_color_coded_box_projections(self, img, cam, corners, color: Tuple[int, int, int]):
        """
        Draw 3D box projections with specified color and proper edge clipping.

        Args:
            img: Input image
            cam: Camera object
            corners: 3D corner points
            color: BGR color tuple for the box
        """
        H, W = img.shape[:2]
        uvz = cam.project(corners)
        pts = []

        # Project all points, keep even those outside image
        for u, v, z in uvz:
            if z <= 0:
                pts.append(None)  # Behind camera
            else:
                pts.append((int(round(u)), int(round(v))))

        # Draw edges with clipping to handle points outside image bounds
        edges = [(0,1),(1,2),(2,3),(3,0),(4,5),(5,6),(6,7),(7,4),(0,4),(1,5),(2,6),(3,7)]
        for i, j in edges:
            if pts[i] is not None and pts[j] is not None:
                # Use cv2.clipLine to handle edge cases
                retval, pt1, pt2 = cv2.clipLine((0, 0, W, H), pts[i], pts[j])
                if retval:
                    cv2.line(img, pt1, pt2, color, 3)  # Thicker lines for better visibility



    def _should_render_object(self, obj_data: Dict, camera_name: str) -> Tuple[bool, str]:
        """
        Determine if an object should be rendered for a specific camera.
        Relaxed filtering - let FOV gating be the primary filter, not per-camera visibility.

        Args:
            obj_data: Object data from JSON
            camera_name: Name of the camera

        Returns:
            (should_render: bool, reason: str)
        """
        visibility = obj_data.get('visibility', {})
        if not visibility:
            return False, "no_visibility_data"

        # Check if object has valid occlusion level
        occlusion_level = visibility.get('occlusion_level')
        if occlusion_level is None:
            return False, "no_occlusion_level"

        # RELAXED: Allow objects with null per-camera visibility to proceed to FOV gating
        # The sphere projection algorithm only computes visibility for cameras where objects are actually visible
        per_camera_visibility = visibility.get('per_camera', {})
        camera_visibility = per_camera_visibility.get(camera_name)

        # Only skip if explicitly set to 0.0 (fully occluded)
        if camera_visibility == 0.0:
            return False, f"per_camera_visibility[{camera_name}] is 0.0 (fully_occluded)"

        # Get per-camera occlusion data for logging (but don't require it)
        per_camera_occlusion = visibility.get('per_camera_occlusion', {})
        camera_occlusion = per_camera_occlusion.get(camera_name)

        # Format visibility and occlusion for logging
        vis_str = f"{camera_visibility:.3f}" if camera_visibility is not None else "null"
        occ_str = f"{camera_occlusion:.3f}" if camera_occlusion is not None else "null"

        return True, f"Level {occlusion_level}, visibility: {vis_str}, occlusion: {occ_str}"


    
    def _visualize_frame(self, json_file: Path) -> bool:
        """
        Visualize a single frame's results.

        Args:
            json_file: Path to the JSON result file

        Returns:
            True if successful, False otherwise
        """
        try:
            # Load JSON data
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if 'result' not in data:
                logger.warning(f"No 'result' field in {json_file}")
                return False

            # FIXED: Access the correct data structure - objects are in data['result']['data']
            result_data = data['result']
            if 'data' not in result_data:
                logger.warning(f"No 'data' field in result section of {json_file}")
                return False

            objects_data = result_data['data']
            if not objects_data:
                logger.warning(f"No objects found in {json_file}")
                return False

            # Extract timestamp from filename
            timestamp = json_file.stem

            # Create frame-specific directory
            frame_dir = self.output_dir / timestamp
            frame_dir.mkdir(parents=True, exist_ok=True)

            # Process each camera
            success_count = 0
            for camera_name, camera in self.cameras.items():
                # Create camera-specific directory
                camera_dir = frame_dir / camera_name
                camera_dir.mkdir(parents=True, exist_ok=True)

                # Find corresponding image
                image_path = self._find_camera_image(timestamp, camera_name)
                if not image_path:
                    continue

                # Load image
                image = cv2.imread(image_path)
                if image is None:
                    logger.warning(f"Failed to load image: {image_path}")
                    continue

                # Create visualization
                vis_image = image.copy()

                # Process each object in the result - Comprehensive filtering with diagnostics
                objects_processed = 0
                objects_drawn = 0
                objects_visibility_filtered = 0
                objects_fov_gated = 0
                objects_projection_failed = 0

                for obj_idx, obj_data in enumerate(objects_data):
                    if 'visibility' not in obj_data:
                        continue

                    objects_processed += 1
                    obj_id = obj_data.get('ObjectID', obj_idx)

                    # STEP 1: Basic visibility validation (relaxed filtering)
                    should_render, visibility_reason = self._should_render_object(obj_data, camera_name)
                    if not should_render:
                        objects_visibility_filtered += 1
                        logger.debug(f"Object {obj_id} visibility filtered from {camera_name}: {visibility_reason}")
                        continue

                    # STEP 2: Create 3D box and apply FOV gating (primary filter)
                    try:
                        box3d = self._create_3d_box_from_json(obj_data)
                        in_fov, fov_reason = self._is_object_in_camera_fov(box3d, camera_name, camera)
                        if not in_fov:
                            objects_fov_gated += 1
                            logger.debug(f"Object {obj_id} FOV gated from {camera_name}: {fov_reason}")
                            continue
                    except Exception as e:
                        objects_projection_failed += 1
                        logger.debug(f"Object {obj_id} 3D box creation failed for {camera_name}: {e}")
                        continue

                    # STEP 3: Get occlusion level and color for rendering
                    visibility = obj_data['visibility']
                    occlusion_level = visibility.get('occlusion_level', 0)
                    box_color = self._get_box_color(occlusion_level)

                    # Get per-camera data for logging
                    per_camera_visibility = visibility.get('per_camera', {})
                    per_camera_occlusion = visibility.get('per_camera_occlusion', {})
                    camera_visibility = per_camera_visibility.get(camera_name)
                    camera_occlusion = per_camera_occlusion.get(camera_name)

                    # STEP 4: Project and draw box with color coding (no labels)
                    try:
                        self._draw_color_coded_box_projections(vis_image, camera, box3d['corners'], box_color)
                        objects_drawn += 1

                        # Enhanced logging with per-camera data
                        vis_str = f"{camera_visibility:.3f}" if camera_visibility is not None else "null"
                        occ_str = f"{camera_occlusion:.3f}" if camera_occlusion is not None else "null"
                        logger.debug(f"Drew object {obj_id} in {camera_name}: level-{occlusion_level}, vis={vis_str}, occ={occ_str}")

                    except Exception as e:
                        objects_projection_failed += 1
                        logger.warning(f"Failed to project object {obj_id} in {camera_name}: {e}")
                        continue

                # Comprehensive logging for all cameras
                logger.info(f"Camera {camera_name}: processed={objects_processed}, vis_filtered={objects_visibility_filtered}, fov_gated={objects_fov_gated}, proj_failed={objects_projection_failed}, drawn={objects_drawn}")

                # Special warnings for cameras with no objects
                if objects_drawn == 0:
                    if objects_processed == 0:
                        logger.warning(f"No objects to process for {camera_name}")
                    elif objects_fov_gated == objects_processed - objects_visibility_filtered:
                        logger.warning(f"All objects FOV gated for {camera_name} - check FOV settings")
                    else:
                        logger.warning(f"No objects drawn for {camera_name} - check filtering logic")
                
                # Save visualization result in camera-specific directory
                output_filename = f"{camera_name}.jpg"
                output_path = camera_dir / output_filename

                success = save_image(vis_image, str(output_path))
                if success:
                    success_count += 1
                    logger.debug(f"Saved visualization: {output_path}")
                else:
                    logger.warning(f"Failed to save: {output_path}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error processing {json_file}: {e}")
            return False
    
    def process_all(self) -> None:
        """
        Process all JSON files in the result directory.
        """
        # Find all JSON files
        json_files = list(self.result_dir.glob("*.json"))
        
        if not json_files:
            logger.error(f"No JSON files found in {self.result_dir}")
            return
        
        logger.info(f"Found {len(json_files)} JSON files to process")
        
        # Process each file
        success_count = 0
        for json_file in tqdm(json_files, desc="Processing frames"):
            if self._visualize_frame(json_file):
                success_count += 1
        
        logger.info(f"Successfully processed {success_count}/{len(json_files)} frames")
        logger.info(f"Results saved to: {self.output_dir}")


def main():
    """
    Main function to run batch visualization.
    """
    # Define paths
    demo_data_dir = "visibility_demo_data/clip_dataset_1"
    result_dir = "result_test/clip_dataset_1"
    output_dir = "outputs/clip_dataset_1"
    calib_file = "visibility_demo_data/clip_dataset_1/calibrated_sensor.pb.txt"
    config_path = "configs/default.yaml"
    
    # Create processor
    processor = BatchVisualizationProcessor(
        demo_data_dir=demo_data_dir,
        result_dir=result_dir,
        output_dir=output_dir,
        calib_file=calib_file,
        config_path=config_path
    )
    
    # Process all files
    processor.process_all()


if __name__ == "__main__":
    main()

# TRAE-MOD END [20250825-1600-vis-opt]
# TRAE-MOD END [20250825-1630-rear-opt]
# TRAE-MOD END [20250825-1700-full-opt]