#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch Visualization Script for Occlusion Results

This script processes JSON result files and visualizes occlusion information
on corresponding camera images using the sphere projection algorithm results.

Author: Trae AI Agent
Date: 2025-01-21
"""

import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
import yaml
from tqdm import tqdm

# Import project modules
from robobus_vis.vis.exporters import save_image
from robobus_vis.io.calib_parser import parse_calibrated_sensor_pb_txt
from robobus_vis.calib.camera_model import Camera

# TRAE-MOD START [20250121-1445-batch-vis]: Purpose - implement batch visualization for occlusion results

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BatchVisualizationProcessor:
    """
    Processes batch visualization of occlusion results on camera images.
    """
    
    def __init__(self, 
                 demo_data_dir: str,
                 result_dir: str,
                 output_dir: str,
                 calib_file: str,
                 config_path: str = None):
        """
        Initialize the batch visualization processor.
        
        Args:
            demo_data_dir: Directory containing camera images (visibility_demo_data/clip_dataset_1)
            result_dir: Directory containing JSON result files (result_test/clip_dataset_1)
            output_dir: Directory to save visualization results
            calib_file: Path to calibrated_sensor.pb.txt file
            config_path: Path to configuration file
        """
        self.demo_data_dir = Path(demo_data_dir)
        self.result_dir = Path(result_dir)
        self.output_dir = Path(output_dir)
        self.calib_file = calib_file
        
        # Load configuration
        if config_path is None:
            config_path = Path(__file__).resolve().parents[0] / 'configs' / 'default.yaml'
        self.config = yaml.safe_load(open(config_path, 'r'))
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Load camera calibrations
        self.cameras = self._load_cameras()
        
        logger.info(f"Initialized BatchVisualizationProcessor")
        logger.info(f"Demo data dir: {self.demo_data_dir}")
        logger.info(f"Result dir: {self.result_dir}")
        logger.info(f"Output dir: {self.output_dir}")
        logger.info(f"Found {len(self.cameras)} cameras")
    
    def _load_cameras(self) -> Dict[str, Camera]:
        """
        Load camera calibrations using the camera mapping from config.
        Maps directory names to calibration sensor names.
        
        Returns:
            Dictionary mapping directory names to Camera objects
        """
        try:
            calibrations = parse_calibrated_sensor_pb_txt(self.calib_file)
            cameras = {}
            
            # Get camera mapping from config
            cam_map = self.config.get('camera_map', {})
            
            for dir_name, sensor_name in cam_map.items():
                if sensor_name not in calibrations.cams:
                    logger.warning(f"Sensor {sensor_name} not found in calibrations")
                    continue
                    
                c = calibrations.cams[sensor_name]
                cameras[dir_name] = Camera(
                    name=dir_name,  # Use directory name, not sensor name
                    K=c['K'],
                    dist=c['dist'],
                    width=c['width'],
                    height=c['height'],
                    T_base_cam=c['T_base_cam'],
                )
                
            logger.info(f"Loaded {len(cameras)} cameras: {list(cameras.keys())}")
            return cameras
            
        except Exception as e:
            logger.error(f"Failed to load camera calibrations: {e}")
            return {}
    
    def _find_camera_image(self, timestamp: str, camera_name: str) -> Optional[str]:
        """
        Find the camera image file for a given timestamp and camera.
        
        Args:
            timestamp: Timestamp string from JSON filename
            camera_name: Name of the camera
            
        Returns:
            Path to the image file if found, None otherwise
        """
        # Direct mapping for known camera names
        camera_dir_mapping = {
            '60_front': '60_front',
            '120_front': '120_front', 
            '120_back': '120_back',
            '120_left': '120_left',
            '120_right': '120_right',
            'left_back': 'left_back',
            'right_back': 'right_back'
        }
        
        # Try direct mapping first
        if camera_name in camera_dir_mapping:
            cam_dir_name = camera_dir_mapping[camera_name]
            cam_dir_path = self.demo_data_dir / cam_dir_name
            
            if cam_dir_path.exists() and cam_dir_path.is_dir():
                # Look for image file with matching timestamp
                image_files = list(cam_dir_path.glob(f"{timestamp}*.jpg"))
                if image_files:
                    return str(image_files[0])
                    
                # Also try without extension matching
                image_files = list(cam_dir_path.glob("*.jpg"))
                for img_file in image_files:
                    if timestamp in img_file.stem:
                        return str(img_file)
        
        # Fallback to fuzzy matching for other cameras
        camera_dirs = [d for d in self.demo_data_dir.iterdir() if d.is_dir()]
        
        for cam_dir in camera_dirs:
            if camera_name.lower() in cam_dir.name.lower() or cam_dir.name.lower() in camera_name.lower():
                # Look for image file with matching timestamp
                image_files = list(cam_dir.glob(f"{timestamp}*.jpg"))
                if image_files:
                    return str(image_files[0])
                    
                # Also try without extension matching
                image_files = list(cam_dir.glob("*.jpg"))
                for img_file in image_files:
                    if timestamp in img_file.stem:
                        return str(img_file)
        
        return None
    
    def _create_3d_box_from_json(self, obj_data: Dict) -> Dict:
        """
        Create a 3D box dictionary from JSON object data with proper rotation handling.

        Args:
            obj_data: Object data from JSON file

        Returns:
            Dictionary with 3D box information
        """
        # Extract center and size
        center = np.array([obj_data['3Dcenter']['x'], obj_data['3Dcenter']['y'], obj_data['3Dcenter']['z']])
        size_data = obj_data['3Dsize']

        # Extract dimensions
        length = size_data['length']
        width = size_data['width']
        height = size_data['height']

        # Extract rotation (use rz for yaw rotation around Z-axis)
        yaw = size_data.get('rz', size_data.get('alpha', 0.0))

        # Create local corner points (before rotation)
        dx, dy, dz = length/2, width/2, height/2
        local_corners = np.array([
            [-dx, -dy, -dz],  # 0: back-left-bottom
            [+dx, -dy, -dz],  # 1: back-right-bottom
            [+dx, +dy, -dz],  # 2: front-right-bottom
            [-dx, +dy, -dz],  # 3: front-left-bottom
            [-dx, -dy, +dz],  # 4: back-left-top
            [+dx, -dy, +dz],  # 5: back-right-top
            [+dx, +dy, +dz],  # 6: front-right-top
            [-dx, +dy, +dz],  # 7: front-left-top
        ])

        # Apply rotation around Z-axis (yaw)
        cos_yaw = np.cos(yaw)
        sin_yaw = np.sin(yaw)
        rotation_matrix = np.array([
            [cos_yaw, -sin_yaw, 0],
            [sin_yaw,  cos_yaw, 0],
            [0,        0,       1]
        ])

        # Rotate corners and translate to world position
        rotated_corners = local_corners @ rotation_matrix.T
        world_corners = rotated_corners + center

        return {
            'center': center,
            'size': np.array([length, width, height]),
            'surface_pts': world_corners,
            'corners': world_corners
        }

    def _draw_enhanced_visibility_label(self, img, label_text, visibility, occlusion_rate, position):
        """
        Draw enhanced visibility label with color coding based on visibility level.

        Args:
            img: Input image
            label_text: Text to display
            visibility: Visibility rate (0.0 to 1.0)
            occlusion_rate: Occlusion rate (0.0 to 1.0)
            position: (x, y) position for the label
        """
        x, y = position

        # Color coding based on visibility level
        if visibility >= 0.7:
            bg_color = (0, 255, 0)  # Green for high visibility
        elif visibility >= 0.4:
            bg_color = (0, 255, 255)  # Yellow for medium visibility
        else:
            bg_color = (0, 0, 255)  # Red for low visibility

        # Calculate text size
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        thickness = 1
        (text_width, text_height), baseline = cv2.getTextSize(label_text, font, font_scale, thickness)

        # Define label background rectangle
        label_bg_top_left = (x, y - text_height - baseline - 4)
        label_bg_bottom_right = (x + text_width + 8, y)

        # Ensure label stays within image bounds
        H, W = img.shape[:2]
        label_bg_top_left = (max(0, label_bg_top_left[0]), max(0, label_bg_top_left[1]))
        label_bg_bottom_right = (min(W, label_bg_bottom_right[0]), min(H, label_bg_bottom_right[1]))

        # Draw colored background rectangle
        cv2.rectangle(img, label_bg_top_left, label_bg_bottom_right, bg_color, -1)

        # Draw black text for better contrast
        text_position = (label_bg_top_left[0] + 4, label_bg_bottom_right[1] - baseline - 2)
        cv2.putText(img, label_text, text_position, font, font_scale, (0, 0, 0), thickness)

    def _should_render_occlusion_object(self, obj_data: Dict, camera_name: str) -> tuple:
        """
        Filter logic for occlusion-level 3 & 4 objects.

        Args:
            obj_data: Object data from JSON
            camera_name: Name of the camera

        Returns:
            (should_render: bool, reason: str)
        """
        visibility = obj_data.get('visibility', {})

        # Check occlusion level requirement
        occlusion_level = visibility.get('occlusion_level')
        if occlusion_level not in [3, 4]:
            return False, f"occlusion_level {occlusion_level} not in [3,4]"

        # Check per-camera occlusion data
        per_camera_occlusion = visibility.get('per_camera_occlusion', {})
        camera_occlusion = per_camera_occlusion.get(camera_name)

        if camera_occlusion is None:
            return False, f"per_camera_occlusion[{camera_name}] is null"

        return True, f"Level {occlusion_level}, occlusion {camera_occlusion:.3f}"

    def _draw_red_white_label(self, img, label_text: str, position: tuple):
        """
        Draw label with red background and white text.

        Args:
            img: Input image
            label_text: Text to display (e.g., "O3:0.87")
            position: (x, y) position for top-left corner
        """
        x, y = position
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 2

        # Calculate text size
        (text_width, text_height), baseline = cv2.getTextSize(
            label_text, font, font_scale, thickness)

        # Ensure label stays within image bounds
        H, W = img.shape[:2]
        x = max(0, min(W - text_width - 8, x))
        y = max(text_height + baseline + 4, min(H, y))

        # Red background rectangle
        bg_top_left = (x, y - text_height - baseline - 4)
        bg_bottom_right = (x + text_width + 8, y)
        cv2.rectangle(img, bg_top_left, bg_bottom_right, (0, 0, 255), -1)  # Red

        # White text
        text_pos = (x + 4, y - baseline - 2)
        cv2.putText(img, label_text, text_pos, font, font_scale, (255, 255, 255), thickness)

    def _draw_complete_box_projections(self, img, cam, corners):
        """
        Draw 3D box projections without truncating at image edges.
        Uses cv2.clipLine to handle edge cases properly.

        Args:
            img: Input image
            cam: Camera object
            corners: 3D corner points
        """
        H, W = img.shape[:2]
        uvz = cam.project(corners)
        pts = []

        # Project all points, keep even those outside image
        for u, v, z in uvz:
            if z <= 0:
                pts.append(None)  # Behind camera
            else:
                pts.append((int(round(u)), int(round(v))))

        # Draw edges with clipping to handle points outside image bounds
        edges = [(0,1),(1,2),(2,3),(3,0),(4,5),(5,6),(6,7),(7,4),(0,4),(1,5),(2,6),(3,7)]
        for i, j in edges:
            if pts[i] is not None and pts[j] is not None:
                # Use cv2.clipLine to handle edge cases
                retval, pt1, pt2 = cv2.clipLine((0, 0, W, H), pts[i], pts[j])
                if retval:
                    cv2.line(img, pt1, pt2, (0, 255, 0), 2)  # Green boxes

    def _is_object_in_camera_fov(self, box3d: Dict, camera: Camera) -> bool:
        """
        Check if a 3D object is within the camera's field of view and reasonable distance.

        Args:
            box3d: 3D bounding box dictionary
            camera: Camera object

        Returns:
            True if object is in FOV and within reasonable distance
        """
        center = box3d['center']

        # Transform center to camera coordinate system
        T_cam_base = np.linalg.inv(camera.T_base_cam)
        center_cam = T_cam_base @ np.append(center, 1)

        # Check if object is in front of camera (positive Z in camera coordinates)
        if center_cam[2] <= 0:
            return False

        # Check distance - objects too far away are likely not accurately visible
        distance = np.linalg.norm(center_cam[:3])
        max_distance = 100.0  # meters - adjust based on your sensor range
        if distance > max_distance:
            return False

        # Project center to image to check if it's within reasonable bounds
        center_2d = camera.project(center.reshape(1, -1))[0]

        # Allow some margin outside image bounds for partially visible objects
        h, w = camera.height, camera.width
        margin_factor = 0.5  # Allow 50% margin outside image bounds
        x_margin = w * margin_factor
        y_margin = h * margin_factor

        if (center_2d[0] < -x_margin or center_2d[0] > w + x_margin or
            center_2d[1] < -y_margin or center_2d[1] > h + y_margin):
            return False

        return True
    
    def _visualize_frame(self, json_file: Path) -> bool:
        """
        Visualize a single frame's results.

        Args:
            json_file: Path to the JSON result file

        Returns:
            True if successful, False otherwise
        """
        try:
            # Load JSON data
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if 'result' not in data:
                logger.warning(f"No 'result' field in {json_file}")
                return False

            # FIXED: Access the correct data structure - objects are in data['result']['data']
            result_data = data['result']
            if 'data' not in result_data:
                logger.warning(f"No 'data' field in result section of {json_file}")
                return False

            objects_data = result_data['data']
            if not objects_data:
                logger.warning(f"No objects found in {json_file}")
                return False

            # Extract timestamp from filename
            timestamp = json_file.stem

            # Create frame-specific directory
            frame_dir = self.output_dir / timestamp
            frame_dir.mkdir(parents=True, exist_ok=True)

            # Process each camera
            success_count = 0
            for camera_name, camera in self.cameras.items():
                # Create camera-specific directory
                camera_dir = frame_dir / camera_name
                camera_dir.mkdir(parents=True, exist_ok=True)

                # Find corresponding image
                image_path = self._find_camera_image(timestamp, camera_name)
                if not image_path:
                    continue

                # Load image
                image = cv2.imread(image_path)
                if image is None:
                    logger.warning(f"Failed to load image: {image_path}")
                    continue

                # Create visualization
                vis_image = image.copy()

                # Process each object in the result - OCCLUSION LEVEL 3 & 4 ONLY
                for obj_idx, obj_data in enumerate(objects_data):
                    if 'visibility' not in obj_data:
                        continue

                    # NEW: Occlusion-based filtering for Sprint #3
                    should_render, reason = self._should_render_occlusion_object(obj_data, camera_name)
                    if not should_render:
                        logger.debug(f"Skipping object {obj_data.get('ObjectID', obj_idx)} in {camera_name}: {reason}")
                        continue

                    # Create 3D box
                    box3d = self._create_3d_box_from_json(obj_data)

                    # NOTE: Removed FOV filtering for occlusion objects to prevent edge truncation

                    # Project and draw box using complete projection (no edge truncation)
                    try:
                        # Use the new complete box projection that handles edges properly
                        self._draw_complete_box_projections(vis_image, camera, box3d['corners'])

                        # Calculate bounding box for label placement
                        corners_2d = camera.project(box3d['corners'])

                        # Filter points with positive depth
                        depth_mask = corners_2d[:, 2] > 0
                        if not np.any(depth_mask):
                            continue  # No points in front of camera

                        valid_corners_2d = corners_2d[depth_mask]

                        # Get image dimensions
                        h, w = image.shape[:2]

                        # Check if any part of the box is visible in the image
                        x_coords = valid_corners_2d[:, 0]
                        y_coords = valid_corners_2d[:, 1]

                        if len(x_coords) == 0:
                            continue

                        # Find position for label placement
                        x_min = int(np.min(x_coords))
                        y_min = int(np.min(y_coords))

                        # Clamp to image bounds for label placement
                        x_min = max(0, min(w - 1, x_min))
                        y_min = max(0, min(h - 1, y_min))

                        # Only add label if we have a valid position
                        if 0 <= x_min < w and 0 <= y_min < h:
                            # Get occlusion data for the new label format
                            visibility = obj_data['visibility']
                            occlusion_level = visibility.get('occlusion_level')
                            per_camera_occlusion = visibility.get('per_camera_occlusion', {})
                            camera_occlusion_rate = per_camera_occlusion.get(camera_name, 0.0)

                            # Create occlusion label in format O{level}:{value:.2f}
                            occlusion_label = f"O{occlusion_level}:{camera_occlusion_rate:.2f}"

                            # Draw red background with white text label
                            self._draw_red_white_label(
                                vis_image,
                                occlusion_label,
                                (x_min, y_min)
                            )

                            logger.debug(f"Drew occlusion box for object {obj_data.get('ObjectID', obj_idx)} in {camera_name}: {occlusion_label}")
                    
                    except Exception as e:
                        logger.debug(f"Failed to project object {obj_idx}: {e}")
                        continue
                
                # Save visualization result in camera-specific directory
                output_filename = f"{camera_name}.jpg"
                output_path = camera_dir / output_filename

                success = save_image(vis_image, str(output_path))
                if success:
                    success_count += 1
                    logger.debug(f"Saved visualization: {output_path}")
                else:
                    logger.warning(f"Failed to save: {output_path}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error processing {json_file}: {e}")
            return False
    
    def process_all(self) -> None:
        """
        Process all JSON files in the result directory.
        """
        # Find all JSON files
        json_files = list(self.result_dir.glob("*.json"))
        
        if not json_files:
            logger.error(f"No JSON files found in {self.result_dir}")
            return
        
        logger.info(f"Found {len(json_files)} JSON files to process")
        
        # Process each file
        success_count = 0
        for json_file in tqdm(json_files, desc="Processing frames"):
            if self._visualize_frame(json_file):
                success_count += 1
        
        logger.info(f"Successfully processed {success_count}/{len(json_files)} frames")
        logger.info(f"Results saved to: {self.output_dir}")


def main():
    """
    Main function to run batch visualization.
    """
    # Define paths
    demo_data_dir = "visibility_demo_data/clip_dataset_1"
    result_dir = "result_test/clip_dataset_1"
    output_dir = "result_test/clip_dataset_1/vis_result"
    calib_file = "visibility_demo_data/clip_dataset_1/calibrated_sensor.pb.txt"
    config_path = "configs/default.yaml"
    
    # Create processor
    processor = BatchVisualizationProcessor(
        demo_data_dir=demo_data_dir,
        result_dir=result_dir,
        output_dir=output_dir,
        calib_file=calib_file,
        config_path=config_path
    )
    
    # Process all files
    processor.process_all()


if __name__ == "__main__":
    main()

# TRAE-MOD END [20250121-1445-batch-vis]