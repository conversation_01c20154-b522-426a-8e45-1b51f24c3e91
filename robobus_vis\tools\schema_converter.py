#!/usr/bin/env python3
# TRAE-MOD START [20250121-1450-cli-tool]: Purpose - command-line interface for schema conversion
"""
Schema Converter CLI Tool

A command-line interface for converting JSON annotation files between different schema formats.
Supports batch processing, multiple output modes, and detailed reporting.

Usage:
    python schema_converter.py convert input.json output.json --mode production
    python schema_converter.py batch input_dir/ output_dir/ --mode training
    python schema_converter.py analyze input.json --compare-with simplified.json
"""

import argparse
import json
import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
import time
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from robobus_vis.config.schema_config import (
    SchemaConfig, 
    OutputMode, 
    SchemaRegistry,
    validate_config,
    get_estimated_size_reduction
)
from robobus_vis.pipeline.simplified_integration import (
    write_simplified_json,
    batch_convert_to_simplified_schema,
    compare_schemas
)
from robobus_vis.visibility.simplified_schema import SchemaVariants


def setup_logging():
    """Set up logging configuration."""
    import logging
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)


def print_banner():
    """Print application banner."""
    print("\n" + "="*60)
    print("    Vision-Aware Annotations Schema Converter")
    print("    Autonomous Driving Perception Pipeline")
    print("="*60 + "\n")


def print_config_info(config: SchemaConfig):
    """Print configuration information."""
    print(f"Configuration:")
    print(f"  Schema Variant: {config.schema_variant}")
    print(f"  Optimize Nulls: {config.optimize_nulls}")
    print(f"  Reduce Precision: {config.reduce_precision} (digits: {config.precision_digits})")
    print(f"  Include Per-Camera: {config.include_per_camera}")
    print(f"  Include Debug Info: {config.include_debug_info}")
    print(f"  Backward Compatible: {config.backward_compatible}")
    
    # Show estimated size reduction
    estimation = get_estimated_size_reduction(config)
    print(f"  Estimated Size Reduction: {estimation['total_estimated_reduction']:.1f}%")
    print()


def validate_input_file(file_path: str) -> bool:
    """Validate input JSON file."""
    if not os.path.exists(file_path):
        print(f"Error: Input file '{file_path}' does not exist.")
        return False
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Basic validation
        if not isinstance(data, dict):
            print(f"Error: Input file '{file_path}' does not contain a valid JSON object.")
            return False
        
        if "result" not in data:
            print(f"Warning: Input file '{file_path}' does not contain 'result' field.")
        
        return True
    
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in file '{file_path}': {e}")
        return False
    except Exception as e:
        print(f"Error: Failed to read file '{file_path}': {e}")
        return False


def convert_single_file(args) -> bool:
    """Convert a single JSON file."""
    logger = setup_logging()
    
    print(f"Converting: {args.input} -> {args.output}")
    
    # Validate input
    if not validate_input_file(args.input):
        return False
    
    # Get configuration
    if args.config:
        config = SchemaRegistry.get(args.config)
        if config is None:
            print(f"Error: Configuration '{args.config}' not found.")
            return False
    else:
        try:
            mode = OutputMode(args.mode)
            config = SchemaConfig.for_mode(mode)
        except ValueError:
            print(f"Error: Invalid mode '{args.mode}'. Valid modes: {[m.value for m in OutputMode]}")
            return False
    
    # Validate configuration
    validation = validate_config(config)
    if not validation["is_valid"]:
        print("Configuration validation failed:")
        for error in validation["errors"]:
            print(f"  Error: {error}")
        return False
    
    if validation["warnings"]:
        print("Configuration warnings:")
        for warning in validation["warnings"]:
            print(f"  Warning: {warning}")
        print()
    
    # Print configuration info
    print_config_info(config)
    
    # Create output directory if needed
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")
    
    # Perform conversion
    start_time = time.time()
    
    try:
        result = write_simplified_json(args.input, args.output, config)
        
        if result["success"]:
            elapsed_time = time.time() - start_time
            
            print(f"✓ Conversion completed successfully in {elapsed_time:.2f}s")
            print(f"  Objects processed: {result['objects_processed']}")
            print(f"  Output file: {args.output}")
            
            # Show file size comparison if requested
            if args.show_stats:
                comparison = compare_schemas(args.input, args.output)
                print(f"\nFile Size Comparison:")
                print(f"  Original: {comparison['size_reduction']['original_size']:,} bytes")
                print(f"  Simplified: {comparison['size_reduction']['new_size']:,} bytes")
                print(f"  Reduction: {comparison['size_reduction']['percentage']:.1f}%")
            
            return True
        else:
            print(f"✗ Conversion failed: {result['error']}")
            return False
    
    except Exception as e:
        print(f"✗ Conversion failed with exception: {e}")
        logger.exception("Conversion failed")
        return False


def convert_batch(args) -> bool:
    """Convert multiple JSON files in batch."""
    logger = setup_logging()
    
    print(f"Batch conversion: {args.input_dir} -> {args.output_dir}")
    
    # Validate input directory
    if not os.path.exists(args.input_dir):
        print(f"Error: Input directory '{args.input_dir}' does not exist.")
        return False
    
    # Find JSON files
    input_files = []
    for root, dirs, files in os.walk(args.input_dir):
        for file in files:
            if file.endswith('.json'):
                input_files.append(os.path.join(root, file))
    
    if not input_files:
        print(f"No JSON files found in '{args.input_dir}'.")
        return False
    
    print(f"Found {len(input_files)} JSON files to process.")
    
    # Get configuration
    if args.config:
        config = SchemaRegistry.get(args.config)
        if config is None:
            print(f"Error: Configuration '{args.config}' not found.")
            return False
    else:
        try:
            mode = OutputMode(args.mode)
            config = SchemaConfig.for_mode(mode)
        except ValueError:
            print(f"Error: Invalid mode '{args.mode}'. Valid modes: {[m.value for m in OutputMode]}")
            return False
    
    # Print configuration info
    print_config_info(config)
    
    # Create output directory
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
        print(f"Created output directory: {args.output_dir}")
    
    # Perform batch conversion
    start_time = time.time()
    
    try:
        results = batch_convert_to_simplified_schema(
            input_files,
            args.output_dir,
            config,
            preserve_structure=args.preserve_structure
        )
        
        elapsed_time = time.time() - start_time
        
        print(f"\nBatch conversion completed in {elapsed_time:.2f}s")
        print(f"  Successful: {len(results['successful'])}")
        print(f"  Failed: {len(results['failed'])}")
        print(f"  Total objects processed: {results['total_objects_processed']}")
        
        if results['failed']:
            print(f"\nFailed files:")
            for file_path, error in results['failed']:
                print(f"  {file_path}: {error}")
        
        # Show aggregate statistics if requested
        if args.show_stats and results['successful']:
            total_original_size = 0
            total_new_size = 0
            
            for file_path in results['successful']:
                original_file = file_path
                simplified_file = os.path.join(
                    args.output_dir,
                    os.path.basename(file_path).replace('.json', '_simplified.json')
                )
                
                if os.path.exists(simplified_file):
                    total_original_size += os.path.getsize(original_file)
                    total_new_size += os.path.getsize(simplified_file)
            
            if total_original_size > 0:
                reduction_percentage = ((total_original_size - total_new_size) / total_original_size) * 100
                print(f"\nAggregate Size Reduction:")
                print(f"  Total original size: {total_original_size:,} bytes")
                print(f"  Total simplified size: {total_new_size:,} bytes")
                print(f"  Total reduction: {reduction_percentage:.1f}%")
        
        return len(results['failed']) == 0
    
    except Exception as e:
        print(f"✗ Batch conversion failed with exception: {e}")
        logger.exception("Batch conversion failed")
        return False


def analyze_file(args) -> bool:
    """Analyze a JSON file and optionally compare with another."""
    print(f"Analyzing: {args.input}")
    
    # Validate input
    if not validate_input_file(args.input):
        return False
    
    try:
        with open(args.input, 'r') as f:
            data = json.load(f)
        
        # Basic analysis
        file_size = os.path.getsize(args.input)
        object_count = len(data.get('result', []))
        
        print(f"\nFile Analysis:")
        print(f"  File size: {file_size:,} bytes")
        print(f"  Object count: {object_count}")
        
        if object_count > 0:
            # Analyze first object structure
            sample_obj = data['result'][0]
            field_count = len(sample_obj)
            
            print(f"  Fields per object: {field_count}")
            print(f"  Average bytes per object: {file_size / object_count:.1f}")
            
            # Show field breakdown
            print(f"\nField Structure:")
            for field, value in sample_obj.items():
                field_type = type(value).__name__
                if isinstance(value, dict):
                    field_type += f" ({len(value)} keys)"
                elif isinstance(value, list):
                    field_type += f" ({len(value)} items)"
                print(f"  {field}: {field_type}")
        
        # Compare with another file if specified
        if args.compare_with:
            if not validate_input_file(args.compare_with):
                return False
            
            print(f"\nComparing with: {args.compare_with}")
            comparison = compare_schemas(args.input, args.compare_with)
            
            print(f"\nComparison Results:")
            print(f"  Size reduction: {comparison['size_reduction']['percentage']:.1f}%")
            print(f"  Original size: {comparison['size_reduction']['original_size']:,} bytes")
            print(f"  New size: {comparison['size_reduction']['new_size']:,} bytes")
            
            if comparison['field_differences']['removed_fields']:
                print(f"  Removed fields: {', '.join(comparison['field_differences']['removed_fields'])}")
            
            if comparison['field_differences']['added_fields']:
                print(f"  Added fields: {', '.join(comparison['field_differences']['added_fields'])}")
        
        return True
    
    except Exception as e:
        print(f"✗ Analysis failed: {e}")
        return False


def list_configurations():
    """List available configurations."""
    print("Available Configurations:")
    print()
    
    # List predefined modes
    print("Predefined Modes:")
    for mode in OutputMode:
        config = SchemaConfig.for_mode(mode)
        estimation = get_estimated_size_reduction(config)
        print(f"  {mode.value}:")
        print(f"    Schema variant: {config.schema_variant}")
        print(f"    Estimated reduction: {estimation['total_estimated_reduction']:.1f}%")
        print(f"    Use case: {_get_mode_description(mode)}")
        print()
    
    # List registered configurations
    registered_configs = SchemaRegistry.list_configs()
    if registered_configs:
        print("Registered Configurations:")
        for name, config in registered_configs.items():
            estimation = get_estimated_size_reduction(config)
            print(f"  {name}:")
            print(f"    Schema variant: {config.schema_variant}")
            print(f"    Estimated reduction: {estimation['total_estimated_reduction']:.1f}%")
            print()
    
    # List schema variants
    print("Schema Variants:")
    for variant in ["essential", "training", "debug", "minimal"]:
        description = SchemaVariants.get_variant_description(variant)
        fields = SchemaVariants.get_variant_fields(variant)
        print(f"  {variant}:")
        print(f"    Description: {description}")
        print(f"    Field count: {len(fields)}")
        print()


def _get_mode_description(mode: OutputMode) -> str:
    """Get description for output mode."""
    descriptions = {
        OutputMode.PRODUCTION: "Minimal schema for production deployment",
        OutputMode.TRAINING: "Optimized for model training with essential metrics",
        OutputMode.DEVELOPMENT: "Full debug information for development",
        OutputMode.ANALYSIS: "Comprehensive metrics for data analysis"
    }
    return descriptions.get(mode, "Unknown mode")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Vision-Aware Annotations Schema Converter",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Convert single file to production schema
  python schema_converter.py convert input.json output.json --mode production
  
  # Batch convert with training schema
  python schema_converter.py batch input_dir/ output_dir/ --mode training
  
  # Analyze file and compare schemas
  python schema_converter.py analyze input.json --compare-with simplified.json
  
  # List available configurations
  python schema_converter.py list-configs
"""
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Convert command
    convert_parser = subparsers.add_parser('convert', help='Convert single JSON file')
    convert_parser.add_argument('input', help='Input JSON file path')
    convert_parser.add_argument('output', help='Output JSON file path')
    convert_parser.add_argument('--mode', default='production', 
                               choices=[m.value for m in OutputMode],
                               help='Output mode (default: production)')
    convert_parser.add_argument('--config', help='Use registered configuration by name')
    convert_parser.add_argument('--show-stats', action='store_true',
                               help='Show file size comparison statistics')
    
    # Batch command
    batch_parser = subparsers.add_parser('batch', help='Batch convert JSON files')
    batch_parser.add_argument('input_dir', help='Input directory path')
    batch_parser.add_argument('output_dir', help='Output directory path')
    batch_parser.add_argument('--mode', default='production',
                             choices=[m.value for m in OutputMode],
                             help='Output mode (default: production)')
    batch_parser.add_argument('--config', help='Use registered configuration by name')
    batch_parser.add_argument('--preserve-structure', action='store_true',
                             help='Preserve directory structure in output')
    batch_parser.add_argument('--show-stats', action='store_true',
                             help='Show aggregate size reduction statistics')
    
    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze JSON file structure')
    analyze_parser.add_argument('input', help='Input JSON file path')
    analyze_parser.add_argument('--compare-with', help='Compare with another JSON file')
    
    # List configurations command
    list_parser = subparsers.add_parser('list-configs', help='List available configurations')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    print_banner()
    
    try:
        if args.command == 'convert':
            success = convert_single_file(args)
        elif args.command == 'batch':
            success = convert_batch(args)
        elif args.command == 'analyze':
            success = analyze_file(args)
        elif args.command == 'list-configs':
            list_configurations()
            success = True
        else:
            print(f"Unknown command: {args.command}")
            success = False
        
        return 0 if success else 1
    
    except KeyboardInterrupt:
        print("\n✗ Operation cancelled by user.")
        return 1
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
# TRAE-MOD END [20250121-1450-cli-tool]