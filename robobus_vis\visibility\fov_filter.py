import numpy as np

DEG = np.pi / 180.0

def wrap_pi(a):
    return (a + np.pi) % (2*np.pi) - np.pi

def ang_diff(a, b):
    return wrap_pi(a - b)


def in_sector(theta, center, half_width):
    return abs(ang_diff(theta, center)) <= half_width


def ego_sector_gate(center_ego, cam_name, sectors):
    x, y = center_ego[0], center_ego[1]
    if x == 0.0 and y == 0.0:
        return False, "object_at_origin"
    theta = np.arctan2(y, x)
    s = sectors.get(cam_name)
    if s is None:
        return True, "no_sector_cfg"
    return in_sector(theta, s["center"], s["half"]), "ok"


def cam_fov_gate_point(center_ego, T_base_cam, cam_name, cam_fov_rad, z_forward_positive=True):
    P_h = np.array([center_ego[0], center_ego[1], center_ego[2], 1.0], dtype=float)
    Pc = (T_base_cam @ P_h)[:3]
    Xc, Yc, Zc = Pc
    if z_forward_positive and Zc <= 0.0:
        return False, "behind_camera"
    theta = np.arctan2(Xc, Zc)
    phi = np.arctan2(Yc, Zc)
    f = cam_fov_rad.get(cam_name)
    if f is None:
        return True, "no_fov_cfg"
    h_half = f["h"] * 0.5
    v_half = f["v"] * 0.5
    if abs(theta) > h_half:
        return False, "outside_hfov"
    if abs(phi) > v_half:
        return False, "outside_vfov"
    return True, "ok"


def cam_fov_gate_corners(corners_ego, T_base_cam, cam_name, cam_fov_rad, z_forward_positive=True):
    P = np.c_[corners_ego, np.ones((corners_ego.shape[0],1))]
    Pc = (T_base_cam @ P.T).T[:, :3]
    Xc, Yc, Zc = Pc[:,0], Pc[:,1], Pc[:,2]
    if z_forward_positive and not np.any(Zc > 0):
        return False, "all_behind"
    theta = np.arctan2(Xc[Zc>0], Zc[Zc>0])
    phi   = np.arctan2(Yc[Zc>0], Zc[Zc>0])
    if theta.size == 0:
        return False, "no_forward_points"
    f = cam_fov_rad.get(cam_name)
    if f is None:
        return True, "no_fov_cfg"
    h_half = f["h"] * 0.5
    v_half = f["v"] * 0.5
    if (np.any(np.abs(theta) <= h_half) and np.any(np.abs(phi) <= v_half)):
        return True, "ok"
    return False, "outside_by_corners"
