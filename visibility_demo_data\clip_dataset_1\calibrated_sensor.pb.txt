base: "base_link"

sensor_info {
  name: "gnss"
  type: GNSS
  orientation: FRONT

  extrinsic {
    translation {
      x: 2.97
      y: -0.73
      z: 0.33
    }
    rotation {
      w: 0.9999675
      x: -0.0078026
      y: -0.0013182
      z: -0.0015287
    }
  }
}

sensor_info {
  name: "H60L-E08160504"
  type: SENSING_60
  orientation: FRONT
  topic: "/sensor/camera/sensing/image_raw_60"
  extrinsic {
    translation {
      x: 4.6
      y: 0.019999999999999997
      z: 2.8
    }
    rotation {
      x: -0.49471315364455881
      y: 0.49254310963333753
      z: -0.50070232312959218
      w: 0.51181770619665568
    }
  }
  intrinsic {
    width: 3840
    height: 2160
    matrix: 3769
    matrix: 0
    matrix: 1821
    matrix: 0
    matrix: 3769
    matrix: 1032
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: -0.36936147289489751
    distort_matrix: 0.041510376164676066
    distort_matrix: 0.0035033469600253288
    distort_matrix: 0.007799621669379822
    distort_matrix: 0
  }
  device: "/dev/video2"
}

sensor_info {
  name: "H30S-E08160500"
  type: SENSING_30
  orientation: FRONT
  topic: "/sensor/camera/sensing/image_raw_30"
  extrinsic {
    translation {
      x: 4.5912103157463342
      y: 0.062375944132440492
      z: 2.6175689578535519
    }
    rotation {
      x: -0.48877867653350754
      y: 0.51726417805975289
      z: -0.49464870394713345
      w: 0.49885452302824967
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 3958.058407221165
    matrix: 0
    matrix: 1037.9511087470105
    matrix: 0
    matrix: 3968.243943909
    matrix: 519.071279024256
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: -0.31599888549882277
    distort_matrix: -0.16668538955913623
    distort_matrix: 0.0010784882169228524
    distort_matrix: 0.0037445586510488878
    distort_matrix: 0
  }
}

sensor_info {
  name: "H120L-E06170513"
  type: SENSING_120
  orientation: FRONT
  topic: "/sensor/camera/sensing/image_raw_120"
  extrinsic {
    translation {
      x: 4.878
      y: -0.025
      z: 1.52
    }
    rotation {
      x: -0.4908258224079477
      y: 0.48472739451585251
      z: -0.51524062966836059
      w: 0.50858279424532882
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 965.06
    matrix: 0
    matrix: 945.72829167948146
    matrix: 0
    matrix: 965.06
    matrix: 560.79530774604723
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: -0.038682849542740143
    distort_matrix: -0.00096019061943750659
    distort_matrix: -0.0013833265374639265
    distort_matrix: -0.00015553554778070596
    distort_matrix: 0
  }
  device: "/dev/video1"
}

sensor_info {
  name: "H120L-E06170599"
  type: SENSING_120
  orientation: REAR
  topic: "/sensor/camera/sensing/image_raw_back"
  extrinsic {
    translation {
      x: -0.95300000000000007
      y: 0.015
      z: 1.56
    }
    rotation {
      x: -0.49058113371945644
      y: -0.48726088659805183
      z: 0.51794943329388032
      w: 0.50362224353255269
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 959.87
    matrix: 0
    matrix: 1012.9514643180771
    matrix: 0
    matrix: 959.87
    matrix: 529.74966766763623
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: -0.037126238395768882
    distort_matrix: 0.00077546165009728782
    distort_matrix: -0.0048988522697234263
    distort_matrix: 0.0012732926799307466
    distort_matrix: 0
  }
  device: "/dev/video7"
}
sensor_info {
  name: "H120L-E06170599_3d"
  type: VIDAR
}
sensor_info {
  name: "H120L-F05160609"
  type: SENSING_120
  orientation: LEFT
  topic: "/sensor/camera/sensing/image_raw_left"
  extrinsic {
    translation {
      x: 3.7146399327067861
      y: 0.99898312566249459
      z: 2.4940666881027291
    }
    rotation {
      x: -0.768284960926692
      y: -0.34621507305254828
      z: 0.14178907655003983
      w: 0.51939310717045251
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 944.03
    matrix: 0
    matrix: 1006.6246743547375
    matrix: 0
    matrix: 944.03
    matrix: 585.596970634353
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: -0.0395199399897791
    distort_matrix: -0.0020813046355665759
    distort_matrix: -5.3003888210981057e-05
    distort_matrix: -0.00056549318880937387
    distort_matrix: 0
  }
  device: "/dev/video3"
}

sensor_info {
  name: "H120L-E12190550"
  type: SENSING_120
  orientation: RIGHT
  topic: "/sensor/camera/sensing/image_raw_right"
  extrinsic {
    translation {
      x: 3.7646399327067859
      y: -0.98898312566249458
      z: 2.5340666881027278
    }
    rotation {
      x: 0.40072824446187816
      y: 0.70933445658596406
      z: -0.56403964514413341
      w: -0.13461345213639136
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 947.81
    matrix: 0
    matrix: 1004.8773886143804
    matrix: 0
    matrix: 947.81
    matrix: 567.03325041266714
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: -0.038571414526183963
    distort_matrix: 0.00083835954567106194
    distort_matrix: -0.0029261378477871531
    distort_matrix: 0.00024641745798411192
    distort_matrix: 0
  }
  device: "/dev/video5"
}

sensor_info {
  name: "H120L-P05160612"
  type: SENSING_120
  orientation: LEFT_BACKWARD
  topic: "/sensor/camera/sensing/image_raw_left_back"
  extrinsic {
    translation {
      x: 0.152
      y: 0.90099999999999991
      z: 2.554
    }
    rotation {
      x: 0.78777147159933925
      y: -0.33819079924492906
      z: 0.16174623409515179
      w: -0.48875479291391494
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 971.03
    matrix: 0
    matrix: 953.24431541196907
    matrix: 0
    matrix: 971.03
    matrix: 549.45015398399687
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: -0.039699866573381752
    distort_matrix: 0.00042525611127712878
    distort_matrix: -0.0037300306368708604
    distort_matrix: 0.0008382745177211901
    distort_matrix: 0
  }
  device: "/dev/video4"
}

sensor_info {
  name: "H120L-F05160614"
  type: SENSING_120
  orientation: RIGHT_BACKWARD
  topic: "/sensor/camera/sensing/image_raw_right_back"
  extrinsic {
    translation {
      x: 0.22200000000000003
      y: -0.901
      z: 2.5540000000000003
    }
    rotation {
      x: -0.37107595974889979
      y: 0.71098567751551567
      z: -0.59067127562896782
      w: 0.088935047147254809
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 962.7
    matrix: 0
    matrix: 1014.2760102272404
    matrix: 0
    matrix: 962.7
    matrix: 537.93435621354149
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: -0.040769798228741147
    distort_matrix: 0.0010657880349491079
    distort_matrix: -0.0031120101627682036
    distort_matrix: 0.00043532323811659864
    distort_matrix: 0
  }
  device: "/dev/video6"
}

sensor_info {
  name: "H190X-G06120634"
  type: SENSING_190
  orientation: FRONT
  topic: "/sensor/camera/sensing/image_raw_fisheye_front"
  extrinsic {
    translation {
      x: 5.03
      y: 0
      z: 0.97
    }
    rotation {
      x: 0.60036975073261212
      y: -0.56801879842493053
      z: 0.410202782064785
      w: -0.38554440033214071
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 1584.41
    matrix: 0
    matrix: 970.7964900582449
    matrix: 0
    matrix: 1584.41
    matrix: 550.11363848448116
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: 1.2551184743055457
    distort_matrix: -1.7085896097204669
    distort_matrix: -0.00040254987989561295
    distort_matrix: 0.0017660037455568933
    distort_matrix: 0
  }
  device: "/dev/video4"
}

sensor_info {
  name: "H190X-G06120632"
  type: SENSING_190
  orientation: LEFT
  topic: "/sensor/camera/sensing/image_raw_fisheye_left"
  extrinsic {
    translation {
      x: -0.6455
      y: 1.1045
      z: 0.7425
    }
    rotation {
      x: -0.8333033643314659
      y: -0.015476235835975903
      z: 0.040267858735479715
      w: 0.55113019212439207
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 1584.1
    matrix: 0
    matrix: 965.93482311204423
    matrix: 0
    matrix: 1584.1
    matrix: 549.4207192229353
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: 1.2528969779726502
    distort_matrix: -1.7001715824051427
    distort_matrix: 0.0010999744831658065
    distort_matrix: 0.0010695263985073819
    distort_matrix: 0
  }
  device: "/dev/video5"
}

sensor_info {
  name: "H190X-G06120626"
  type: SENSING_190
  orientation: RIGHT
  topic: "/sensor/camera/sensing/image_raw_fisheye_right"
  extrinsic {
    translation {
      x: -0.6455
      y: -1.1045
      z: 0.7425
    }
    rotation {
      x: -0.012788024124832651
      y: 0.842520489427203
      z: -0.53795088325327622
      w: -0.02458736548206952
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 1595.96
    matrix: 0
    matrix: 967.580172932329
    matrix: 0
    matrix: 1595.96
    matrix: 560.976561688951
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: 1.3095911578733932
    distort_matrix: -1.814871376117466
    distort_matrix: 0.0033823380690079083
    distort_matrix: 0.0027937263377204552
    distort_matrix: 0
  }
  device: "/dev/video6"
}

sensor_info {
  name: "H190X-G06120635"
  type: SENSING_190
  orientation: REAR
  topic: "/sensor/camera/sensing/image_raw_fisheye_back"
  extrinsic {
    translation {
      x: -0.995
      y: 0
      z: 0.97
    }
    rotation {
      x: 0.61605411285014833
      y: 0.60554994743526513
      z: -0.35414706321443412
      w: -0.35828263817572309
    }
  }
  intrinsic {
    width: 1920
    height: 1080
    matrix: 1588.01
    matrix: 0
    matrix: 965.09556878661749
    matrix: 0
    matrix: 1588.01
    matrix: 549.534181056255
    matrix: 0
    matrix: 0
    matrix: 1
  }
  distcoeff {
    distort_matrix: 1.2559857774777319
    distort_matrix: -1.7071877220886489
    distort_matrix: 0.001442666764594367
    distort_matrix: 0.0014503690643092098
    distort_matrix: 0
  }
  device: "/dev/video7"
}

sensor_info {
  name: "c32_front_left"
  type: LSLIDAR_C32
  orientation: LEFT_FORWARD
  topic: "/sensor/lidar/front_left/point_cloud"
  extrinsic {
    translation {
      x: 5.03
      y: 1.15
      z: 0.7
    }
    rotation {#-2.1	-2.65	119.95
      x: 0.0108502
      y: -0.0274302
      z: 0.8652183
      w: 0.5005269
    }
  }
}

sensor_info {
  name: "c32_front_right"
  type: LSLIDAR_C32
  orientation: RIGHT_FORWARD
  topic: "/sensor/lidar/front_right/point_cloud"
  extrinsic {
    translation {
      x: 5.15
      y: -0.9
      z: 0.71
    }
    rotation {#-0.25	-1.15	58,8
      x: 0.0030258
      y: -0.009814
      z:  0.4908588
      w: 0.8711786


    }
  }
}

sensor_info {
  name: "c32_rear"
  type: LSLIDAR_C32
  orientation: REAR
  topic: "/sensor/lidar/rear/point_cloud"
  extrinsic {
    translation {
      x: -1.0
      y: -0.15
      z: 0.75
    }
    rotation {#0.2	-0.4	272.6
      x: -0.00114982
      y: 0.00372943
      z: 0.690873
      w:-0.722966
    }
  }
}

sensor_info{
  name:"lidar_main"
  type:LSLIDAR_C32
}

sensor_info{
  name:"lidar_zvision"
  type:ZVISIONLIDAR
}

sensor_info{
  name:"lidar_rsm1"
  type:RSLIDAR_M1
}

sensor_info {
  name: "radar_front"
  type: CONTI_RADAR_ARS408
  orientation: FRONT
  topic: "perception/radar/front/radar_obstacle"
  extrinsic {
    translation {
      x: 5
      y: 0
      z: 0.512
    }
    rotation {
      x: 0
      y: 0
      z: -0.013264113343632795
      w: 0.99991202777904775
    }
  }
}

sensor_info {
  name: "radar_rear_left"
  type: CONTI_RADAR_SRR308
  orientation: LEFT_BACKWARD
  topic: "perception/radar/rear_left/radar_obstacle"
  extrinsic {
    translation {
      x: -0.79
      y: 0.96
      z: 0.68
    }
    rotation {
      x: 0
      y: 0
      z: 0.95630475596303477
      w: 0.2923717047227391
    }
  }
}

sensor_info {
  name: "radar_rear_right"
  type: CONTI_RADAR_SRR308
  orientation: RIGHT_BACKWARD
  topic: "perception/radar/rear_right/radar_obstacle"
  extrinsic {
    translation {
      x: -0.79
      y: -0.96
      z: 0.68
    }
    rotation {
      x: 0
      y: 0
      z: -0.95501994445718585
      w: 0.29654157497557349
    }
  }
}
sensor_info {
  name: "radar_front_left"
  type: CONTI_RADAR_SRR308
  orientation: LEFT_FORWARD
  topic: "/perception/radar/front_left/radar_obstacle"
  extrinsic {
    translation {
      x: 4.832
      y: 0.96299999999999986
      z: 0.688
    }
    rotation {
      x: 0
      y: 0
      z: 0.23768589232617129
      w: 0.97134206981326188
    }
  }
}

sensor_info {
  name: "radar_front_right"
  type: CONTI_RADAR_SRR308
  orientation: RIGHT_FORWARD
  topic: "/perception/radar/front_right/radar_obstacle"
  extrinsic {
    translation {
      x: 4.83
      y: -0.96
      z: 0.68
    }
    rotation {
      x: 0
      y: 0
      z: -0.36162457008209214
      w: 0.93232380121551228
    }
  }
}

sensor_info {
  name: "radar_rear"
  type: CONTI_RADAR_ARS408
  orientation: REAR
  topic: "/perception/radar/rear/radar_obstacle"
  extrinsic {
    translation {
      x: -1.02
      y: -0.05
      z: 0.54
    }
    rotation {
      x: 0
      y: 0
      z: 0.99999986292216425
      w: 0.00052359875167328753
    }
  }
}

sensor_info {
  name: "obu"
  type: V2X
  orientation: FRONT
  topic: "/perception/v2x/v2x_obstacle"
  extrinsic {
    translation {
      x: 0.0
      y: 0.0
      z: 0.0
    }
    rotation {
      w: 1.00000
      x: 0.00000
      y: 0.00000
      z: 0.00000
    }
  }
}

sensor_info {
  name: "innolidar"
  type: INNOVUSION_FALCON_LIDAR
  orientation: FRONT
  topic: "/sensor/lidar/innolidar/point_cloud"
  extrinsic {
    translation {
      x: 4.67
      y: 0.00
      z: 2.67
    }
    rotation {
      w: 0.9989518
      x: 0.0074406
      y: 0.0442983
      z: 0.0088062
    }
  }
}

sensor_info {
  name: "zvisionlidar_front"
  type: ZVISIONLIDAR
  orientation: FRONT
  topic: "/sensor/lidar/zvisionlidar/front/point_cloud"
  extrinsic {
    translation {
      x: 4.70
      y: -0.03
      z: 2.74
    }
    rotation {
      w: 0.698528
      x: -0.1137146
      y: 0.1297921
      z: -0.694465
    }
  }
}

sensor_info {
  name: "zvisionlidar_left"
  type: ZVISIONLIDAR
  orientation: LEFT
  topic: "/sensor/lidar/zvisionlidar/left/point_cloud"
  extrinsic {
    translation {
      x: 2.01
      y: 0.90
      z: 2.60
    }
    rotation {
      w: 0.9766144
      x: -0.2147712
      y: 0.0042982
      z: 0.008897
    }
  }
}

sensor_info {
  name: "zvisionlidar_right"
  type: ZVISIONLIDAR
  orientation: RIGHT
  topic: "/sensor/lidar/zvisionlidar/right/point_cloud"
  extrinsic {
    translation {
      x: 2.07
      y: -0.96
      z:  2.6
    }
    rotation {
      w: 0.0214583
      x: 0.0075648
      y: 0.2259713
      z: -0.9738682
    }
  }
}

sensor_info {
  name: "zvisionlidar_rear"
  type: ZVISIONLIDAR
  orientation: REAR
  topic: "/sensor/lidar/zvisionlidar/rear/point_cloud"
  extrinsic {
    translation {
      x: -0.62
      y: -0.03
      z:  2.65
    }
    rotation {
      w: 0.6690974
      x: -0.1217368
      y: -0.1274446
      z: 0.7219742
    }
  }
}

sensor_info {
  name: "rslidar_left"
  type: RSLIDAR_M1
  orientation: LEFT
  topic: "/sensor/lidar/rslidar/left/point_cloud"
  extrinsic {
    translation {
      x: 4.10
      y: 0.90
      z: 2.62
    }
    rotation {
      w: 0.6852416
      x: -0.1058977
      y: 0.0981375
      z: 0.7138618
    }
  }
}

sensor_info {
  name: "rslidar_right"
  type: RSLIDAR_M1
  orientation: RIGHT
  topic: "/sensor/lidar/rslidar/right/point_cloud"
  extrinsic {
    translation {
      x: 4.12
      y: -0.90
      z: 2.55
    }
    rotation {
      w: 0.6981268
      x: 0.1004796
      y: 0.0901975
      z: -0.7031267
    }
  }
}

sensor_info {
  name: "rslidar_rear"
  type: RSLIDAR_M1
  orientation: REAR
  topic: "/sensor/lidar/rslidar/rear/point_cloud"
  extrinsic {
    translation {
      x: -0.62
      y: 0.00
      z: 2.56
    }
    rotation {
      w: -0.0196434
      x: -0.1563214
      y: -0.0073119
      z: 0.9874838
    }
  }
}

sensor_info {
  name: "hesai_at128_front"
  type: HESAI_128
  orientation: FRONT
  topic: "/sensor/lidar/hesai_at128/front/point_cloud"
  extrinsic {
    translation {
      x: 4.68
      y: 0.00
      z: 2.67
    }
    rotation {
      w: 0.9999942
      x: 0.0026157
      y: -0.0017487
      z: -0.0013044
    }
  }
}

sensor_info {
  name: "hesai_at128_left"
  type: HESAI_128
  orientation: LEFT
  topic: "/sensor/lidar/hesai_at128/left/point_cloud"
  extrinsic {
    translation {
      x: 4.10
      y: 0.93
      z: 2.70
    }
    rotation {
      w: 0.6877079
      x: -0.0621776
      y: 0.0617173
      z: 0.7206822
    }
  }
}

sensor_info {
  name: "hesai_at128_right"
  type: HESAI_128
  orientation: RIGHT
  topic: "/sensor/lidar/hesai_at128/right/point_cloud"
  extrinsic {
    translation {
      x: 4.20
      y: -0.98
      z: 2.70
    }
    rotation {
      w: 0.6983549
      x: 0.0676264
      y: 0.0592547
      z:-0.7100817
    }
  }
}

sensor_info {
  name: "hesai_at128_back"
  type: HESAI_128
  orientation: REAR
  topic: "/sensor/lidar/hesai_at128/back/point_cloud"
  extrinsic {
    translation {
      x: -0.60
      y: 0.00
      z: 2.73
    }
    rotation {
      w: -0.0235879
      x: -0.0112795
      y: -0.0028844
      z: 0.999654
    }
  }
}

sensor_info {
  name: "robin_left"
  type: INNOVUSION_FALCON_LIDAR
  orientation: LEFT
  topic: "/sensor/lidar/robin_left/point_cloud"
  extrinsic {
    translation {
      x: 2.00
      y: 0.84
      z: 2.50
    }
    rotation {
      w: 0.3860155
      x: 0.5939241
      y: 0.5901075
      z: -0.3873233
    }
  }
}

sensor_info {
  name: "robin_right"
  type: INNOVUSION_FALCON_LIDAR
  orientation: RIGHT
  topic: "/sensor/lidar/robin_right/point_cloud"
  extrinsic {
    translation {
      x: 2.04
      y: -0.80
      z: 2.53
    }
    rotation {
      w: -0.388914
      x: 0.5983831
      y: -0.5827631
      z: -0.3886782
    }
  }
}

sensor_info {
  name: "robin_rear"
  type: INNOVUSION_FALCON_LIDAR
  orientation: REAR
  topic: "/sensor/lidar/robin_rear/point_cloud"
  extrinsic {
    translation {
       x: -0.64
      y: 0.00
      z: 2.85
    }
    rotation {
      w: -0.0236536
      x: -0.1752567
      y: -0.0067083
      z: 0.9842157
    }
  }
}

sensor_info {
  name: "robin_front"
  type: INNOVUSION_FALCON_LIDAR
  orientation: FRONT
  topic: "/sensor/lidar/robin_front/point_cloud"
  extrinsic {
    translation {
      x: 4.68
      y: 0.00
      z: 2.73
    }
    rotation {
      w: 0.9856855
      x: 0.0041759
      y: 0.1684717
      z: -0.004889
    }
  }
}
